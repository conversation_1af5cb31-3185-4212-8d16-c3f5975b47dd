#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
run_enhanced_training.py

Script to run enhanced training with optimal parameters for achieving
loss values below 0.01 with the new 8-layer model.
"""

import os
import sys
import subprocess
import argparse
import time


def run_training_experiment(config):
    """Run a training experiment with given configuration"""
    
    print(f"\n{'='*60}")
    print(f"RUNNING ENHANCED TRAINING EXPERIMENT")
    print(f"{'='*60}")
    print(f"Configuration: {config['name']}")
    print(f"Target Loss: {config['target_loss']}")
    print(f"Loss Function: {config['loss_type']}")
    print(f"Learning Rate: {config['lr']}")
    print(f"Num Steps: {config['num_steps']}")
    print(f"{'='*60}\n")
    
    # Create command
    cmd = [
        'python3', 'enhanced_train.py',
        '--lr', str(config['lr']),
        '--weight_decay', str(config['weight_decay']),
        '--num_robots', str(config['num_robots']),
        '--num_steps', str(config['num_steps']),
        '--batch_size', str(config['batch_size']),
        '--checkpoint_interval', str(config['checkpoint_interval']),
        '--cpsave', config['checkpoint_dir'],
        '--loss_type', config['loss_type'],
        '--target_loss', str(config['target_loss']),
        '--dropout', str(config['dropout']),
        '--num_heads', str(config['num_heads'])
    ]
    
    if config.get('use_warmup', False):
        cmd.append('--use_warmup')
    
    # Run training
    start_time = time.time()
    try:
        result = subprocess.run(cmd, capture_output=True, text=True, timeout=7200)  # 2 hour timeout
        end_time = time.time()
        
        print(f"Training completed in {end_time - start_time:.2f} seconds")
        
        if result.returncode == 0:
            print("✅ Training successful!")
            print("STDOUT:", result.stdout[-1000:])  # Last 1000 characters
        else:
            print("❌ Training failed!")
            print("STDERR:", result.stderr)
            
        return result.returncode == 0, result.stdout, result.stderr
        
    except subprocess.TimeoutExpired:
        print("⏰ Training timed out after 2 hours")
        return False, "", "Training timed out"
    except Exception as e:
        print(f"❌ Error running training: {e}")
        return False, "", str(e)


def main():
    parser = argparse.ArgumentParser(description='Run Enhanced Training Experiments')
    parser.add_argument('--experiment', type=str, default='aggressive',
                       choices=['conservative', 'balanced', 'aggressive', 'all'],
                       help='Which experiment configuration to run')
    parser.add_argument('--base_dir', type=str, default='./experiments_enhanced',
                       help='Base directory for experiments')
    
    args = parser.parse_args()
    
    # Create base experiment directory
    os.makedirs(args.base_dir, exist_ok=True)
    
    # Define experiment configurations
    experiments = {
        'conservative': {
            'name': 'Conservative Training',
            'lr': 5e-5,
            'weight_decay': 1e-6,
            'num_robots': 2,
            'num_steps': 3000,
            'batch_size': 16,
            'checkpoint_interval': 100,
            'checkpoint_dir': os.path.join(args.base_dir, 'conservative'),
            'loss_type': 'multi_objective',
            'target_loss': 0.008,
            'dropout': 0.05,
            'num_heads': 8,
            'use_warmup': True
        },
        
        'balanced': {
            'name': 'Balanced Training',
            'lr': 1e-4,
            'weight_decay': 5e-7,
            'num_robots': 2,
            'num_steps': 4000,
            'batch_size': 24,
            'checkpoint_interval': 100,
            'checkpoint_dir': os.path.join(args.base_dir, 'balanced'),
            'loss_type': 'multi_objective',
            'target_loss': 0.005,
            'dropout': 0.1,
            'num_heads': 8,
            'use_warmup': True
        },
        
        'aggressive': {
            'name': 'Aggressive Training',
            'lr': 2e-4,
            'weight_decay': 1e-7,
            'num_robots': 2,
            'num_steps': 5000,
            'batch_size': 32,
            'checkpoint_interval': 50,
            'checkpoint_dir': os.path.join(args.base_dir, 'aggressive'),
            'loss_type': 'multi_objective',
            'target_loss': 0.003,
            'dropout': 0.15,
            'num_heads': 12,
            'use_warmup': True
        }
    }
    
    # Run experiments
    results = {}
    
    if args.experiment == 'all':
        experiment_list = ['conservative', 'balanced', 'aggressive']
    else:
        experiment_list = [args.experiment]
    
    for exp_name in experiment_list:
        config = experiments[exp_name]
        
        # Create checkpoint directory
        os.makedirs(config['checkpoint_dir'], exist_ok=True)
        
        # Run experiment
        success, stdout, stderr = run_training_experiment(config)
        results[exp_name] = {
            'success': success,
            'stdout': stdout,
            'stderr': stderr,
            'config': config
        }
        
        # Save experiment log
        log_file = os.path.join(config['checkpoint_dir'], 'experiment_log.txt')
        with open(log_file, 'w') as f:
            f.write(f"Experiment: {config['name']}\n")
            f.write(f"Success: {success}\n")
            f.write(f"Configuration: {config}\n\n")
            f.write("STDOUT:\n")
            f.write(stdout)
            f.write("\n\nSTDERR:\n")
            f.write(stderr)
        
        print(f"Experiment log saved to: {log_file}")
    
    # Print summary
    print(f"\n{'='*60}")
    print("EXPERIMENT SUMMARY")
    print(f"{'='*60}")
    
    for exp_name, result in results.items():
        status = "✅ SUCCESS" if result['success'] else "❌ FAILED"
        print(f"{exp_name.upper():12} | {status}")
        
        # Try to extract final loss from stdout
        if result['success'] and 'Final loss:' in result['stdout']:
            try:
                lines = result['stdout'].split('\n')
                for line in lines:
                    if 'Final loss:' in line:
                        final_loss = line.split('Final loss:')[1].strip()
                        print(f"             | Final Loss: {final_loss}")
                        break
            except:
                pass
    
    print(f"{'='*60}")
    
    # Generate analysis plots for successful experiments
    print("\nGenerating analysis plots for successful experiments...")
    for exp_name, result in results.items():
        if result['success']:
            config = result['config']
            checkpoint_dir = config['checkpoint_dir']
            
            # Find the latest checkpoint
            checkpoints = [f for f in os.listdir(checkpoint_dir) if f.startswith('enhanced_checkpoint_')]
            if checkpoints:
                latest_checkpoint = sorted(checkpoints)[-1]
                checkpoint_path = os.path.join(checkpoint_dir, latest_checkpoint)
                output_plot = os.path.join(checkpoint_dir, f'{exp_name}_analysis.png')
                
                try:
                    plot_cmd = [
                        'python3', 'plot_enhanced_training.py',
                        '--checkpoint', checkpoint_path,
                        '--output', output_plot
                    ]
                    subprocess.run(plot_cmd, check=True)
                    print(f"✅ Analysis plot generated: {output_plot}")
                except Exception as e:
                    print(f"❌ Failed to generate plot for {exp_name}: {e}")
    
    print("\nAll experiments completed!")
    print(f"Results saved in: {args.base_dir}")


if __name__ == '__main__':
    main()
