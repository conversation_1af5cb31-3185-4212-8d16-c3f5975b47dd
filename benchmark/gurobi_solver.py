import numpy as np
import os
import gurobipy as gp
from gurobipy import GRB

def solve_instance_gurobi(instance_path, num_robots, timeout=300):
    try:
        loc_path = instance_path + '_l.txt'
        dur_path = instance_path + '_d.txt'
        prec_path = instance_path + '_p.txt'

        loc = np.loadtxt(loc_path, dtype=int)
        dur = np.loadtxt(dur_path, dtype=int)
        prec = np.loadtxt(prec_path, dtype=int)

        num_tasks = len(dur)

        model = gp.Model("MRC_Scheduling")
        model.Params.LogToConsole = 0
        model.Params.TimeLimit = timeout

        # Decision variables: start time of tasks and assignment to robots
        s = model.addVars(num_tasks, vtype=GRB.CONTINUOUS, name="start")
        x = model.addVars(num_tasks, num_robots, vtype=GRB.BINARY, name="assign")

        M = 1e5  # big M

        # Each task is assigned to exactly one robot
        for i in range(num_tasks):
            model.addConstr(gp.quicksum(x[i, r] for r in range(num_robots)) == 1)

        # Precedence constraints
        for i, j in prec:
            model.addConstr(s[j] >= s[i] + dur[i])

        # Mutual exclusion constraints
        for i in range(num_tasks):
            for j in range(i+1, num_tasks):
                for r in range(num_robots):
                    model.addConstr(
                        s[i] + dur[i] <= s[j] + M * (2 - x[i, r] - x[j, r]) |
                        s[j] + dur[j] <= s[i] + M * (2 - x[i, r] - x[j, r])
                    )

        # Objective: minimize makespan
        makespan = model.addVar(vtype=GRB.CONTINUOUS, name="makespan")
        for i in range(num_tasks):
            model.addConstr(makespan >= s[i] + dur[i])
        model.setObjective(makespan, GRB.MINIMIZE)

        model.optimize()

        if model.status == GRB.OPTIMAL:
            schedule = [[] for _ in range(num_robots)]
            task_order = np.zeros(num_tasks, dtype=int)

            for i in range(num_tasks):
                for r in range(num_robots):
                    if x[i, r].X > 0.5:
                        schedule[r].append((s[i].X, i))
                        break

            for r in range(num_robots):
                schedule[r].sort()
                for t_idx, (_, task_id) in enumerate(schedule[r]):
                    task_order[task_id] = t_idx

            return True, schedule, task_order
        else:
            return False, None, None

    except Exception as e:
        print(f"[ERROR] While solving {instance_path}: {e}")
        return False, None, None

