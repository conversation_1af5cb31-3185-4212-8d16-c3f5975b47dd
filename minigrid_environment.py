#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
minigrid_environment.py

MiniGrid-based simulation environment for decentralized multi-robot task scheduling.
N autonomous agents cooperatively discover and visit M goals in an L×L grid.
"""

import numpy as np
import matplotlib.pyplot as plt
import matplotlib.patches as patches
from matplotlib.animation import FuncAnimation
import random
import os
from typing import List, Tuple, Dict, Optional
import time


class GridWorld:
    """
    Grid-based environment for multi-robot task scheduling simulation.
    """
    
    def __init__(self, grid_size: int = 10, num_robots: int = 2, num_goals: int = 5):
        self.grid_size = grid_size
        self.num_robots = num_robots
        self.num_goals = num_goals
        
        # Grid state: 0=empty, 1=obstacle, 2=goal, 3=robot
        self.grid = np.zeros((grid_size, grid_size), dtype=int)
        
        # Robot and goal positions
        self.robot_positions = []
        self.goal_positions = []
        self.visited_goals = set()
        
        # Task assignments (goal_id -> robot_id)
        self.task_assignments = {}
        
        # Movement history for visualization
        self.robot_paths = [[] for _ in range(num_robots)]
        
        # Initialize environment
        self._initialize_environment()
    
    def _initialize_environment(self):
        """Initialize robot and goal positions randomly."""
        # Clear grid
        self.grid.fill(0)
        
        # Place obstacles (optional - can be added for complexity)
        num_obstacles = max(1, self.grid_size // 3)
        for _ in range(num_obstacles):
            while True:
                x, y = random.randint(0, self.grid_size-1), random.randint(0, self.grid_size-1)
                if self.grid[x, y] == 0:
                    self.grid[x, y] = 1  # Obstacle
                    break
        
        # Place robots
        self.robot_positions = []
        for robot_id in range(self.num_robots):
            while True:
                x, y = random.randint(0, self.grid_size-1), random.randint(0, self.grid_size-1)
                if self.grid[x, y] == 0:
                    self.robot_positions.append((x, y))
                    self.grid[x, y] = 3  # Robot
                    self.robot_paths[robot_id] = [(x, y)]
                    break
        
        # Place goals
        self.goal_positions = []
        for goal_id in range(self.num_goals):
            while True:
                x, y = random.randint(0, self.grid_size-1), random.randint(0, self.grid_size-1)
                if self.grid[x, y] == 0:
                    self.goal_positions.append((x, y))
                    self.grid[x, y] = 2  # Goal
                    break
        
        self.visited_goals = set()
        self.task_assignments = {}
    
    def get_robot_observation(self, robot_id: int, vision_range: int = 3) -> Dict:
        """
        Get local observation for a specific robot.
        
        Args:
            robot_id: ID of the robot
            vision_range: How far the robot can see
            
        Returns:
            Dictionary containing local observation
        """
        robot_pos = self.robot_positions[robot_id]
        x, y = robot_pos
        
        # Local grid observation
        local_grid = np.zeros((2*vision_range+1, 2*vision_range+1), dtype=int)
        
        for dx in range(-vision_range, vision_range+1):
            for dy in range(-vision_range, vision_range+1):
                nx, ny = x + dx, y + dy
                if 0 <= nx < self.grid_size and 0 <= ny < self.grid_size:
                    local_grid[dx+vision_range, dy+vision_range] = self.grid[nx, ny]
                else:
                    local_grid[dx+vision_range, dy+vision_range] = -1  # Out of bounds
        
        # Visible goals
        visible_goals = []
        for goal_id, goal_pos in enumerate(self.goal_positions):
            if goal_id not in self.visited_goals:
                gx, gy = goal_pos
                if abs(gx - x) <= vision_range and abs(gy - y) <= vision_range:
                    visible_goals.append({
                        'id': goal_id,
                        'position': goal_pos,
                        'distance': abs(gx - x) + abs(gy - y)  # Manhattan distance
                    })
        
        # Visible other robots
        visible_robots = []
        for other_id, other_pos in enumerate(self.robot_positions):
            if other_id != robot_id:
                ox, oy = other_pos
                if abs(ox - x) <= vision_range and abs(oy - y) <= vision_range:
                    visible_robots.append({
                        'id': other_id,
                        'position': other_pos,
                        'distance': abs(ox - x) + abs(oy - y)
                    })
        
        return {
            'robot_id': robot_id,
            'position': robot_pos,
            'local_grid': local_grid,
            'visible_goals': visible_goals,
            'visible_robots': visible_robots,
            'assigned_goal': self.task_assignments.get(robot_id, None)
        }
    
    def move_robot(self, robot_id: int, action: int) -> bool:
        """
        Move robot based on action.
        
        Args:
            robot_id: ID of the robot to move
            action: 0=stay, 1=up, 2=right, 3=down, 4=left
            
        Returns:
            True if move was successful, False otherwise
        """
        if robot_id >= len(self.robot_positions):
            return False
        
        x, y = self.robot_positions[robot_id]
        
        # Clear current position
        self.grid[x, y] = 0
        
        # Calculate new position
        if action == 1:  # Up
            new_x, new_y = max(0, x-1), y
        elif action == 2:  # Right
            new_x, new_y = x, min(self.grid_size-1, y+1)
        elif action == 3:  # Down
            new_x, new_y = min(self.grid_size-1, x+1), y
        elif action == 4:  # Left
            new_x, new_y = x, max(0, y-1)
        else:  # Stay (action == 0)
            new_x, new_y = x, y
        
        # Check if new position is valid (not obstacle or another robot)
        if self.grid[new_x, new_y] in [0, 2]:  # Empty or goal
            # Check if there's a goal at this position
            goal_at_position = None
            for goal_id, goal_pos in enumerate(self.goal_positions):
                if goal_pos == (new_x, new_y) and goal_id not in self.visited_goals:
                    goal_at_position = goal_id
                    break
            
            # Update robot position
            self.robot_positions[robot_id] = (new_x, new_y)
            self.robot_paths[robot_id].append((new_x, new_y))
            self.grid[new_x, new_y] = 3  # Robot
            
            # Check if robot reached a goal
            if goal_at_position is not None:
                self.visited_goals.add(goal_at_position)
                print(f"Robot {robot_id} reached goal {goal_at_position} at position {(new_x, new_y)}")
                return True
            
            return True
        else:
            # Invalid move, stay in place
            self.grid[x, y] = 3  # Robot stays
            return False
    
    def assign_task(self, robot_id: int, goal_id: int):
        """Assign a goal to a robot."""
        if goal_id not in self.visited_goals:
            self.task_assignments[robot_id] = goal_id
    
    def get_state(self) -> Dict:
        """Get current state of the environment."""
        return {
            'grid': self.grid.copy(),
            'robot_positions': self.robot_positions.copy(),
            'goal_positions': self.goal_positions.copy(),
            'visited_goals': self.visited_goals.copy(),
            'task_assignments': self.task_assignments.copy(),
            'completion_rate': len(self.visited_goals) / self.num_goals
        }
    
    def is_complete(self) -> bool:
        """Check if all goals have been visited."""
        return len(self.visited_goals) == self.num_goals
    
    def reset(self):
        """Reset the environment."""
        self._initialize_environment()


class GridWorldVisualizer:
    """
    Enhanced visualizer for the grid world environment with modern look and feel.
    """

    def __init__(self, grid_world: GridWorld):
        self.grid_world = grid_world
        # Create figure with dark theme
        plt.style.use('dark_background')
        self.fig, self.ax = plt.subplots(figsize=(12, 10))
        self.fig.patch.set_facecolor('#1a1a1a')  # Dark background

        # Modern color palette
        self.robot_colors = ['#00d4ff', '#ff6b35', '#7b68ee', '#32cd32', '#ff69b4']  # Bright modern colors
        self.goal_colors = ['#ffd700', '#ff4500', '#00ff7f', '#ff1493', '#00bfff']   # Vibrant goal colors
        self.grid_color = '#404040'      # Subtle grid lines
        self.text_color = '#ffffff'      # White text
        self.bg_color = '#2d2d2d'        # Dark gray background
        self.obstacle_color = '#8b4513'  # Brown obstacles
        self.path_alpha = 0.7            # Path transparency
        
    def render(self, show_paths: bool = True, show_assignments: bool = True,
               task_info: dict = None, step_count: int = 0):
        """Render the current state of the grid world with enhanced modern look and feel."""
        self.ax.clear()

        grid_size = self.grid_world.grid_size

        # Set modern dark background with gradient effect
        self.ax.set_facecolor(self.bg_color)

        # Add subtle gradient background
        gradient = np.linspace(0, 1, 100).reshape(1, -1)
        gradient = np.vstack((gradient, gradient))
        self.ax.imshow(gradient, extent=[-0.5, grid_size-0.5, -0.5, grid_size-0.5],
                      aspect='auto', alpha=0.1, cmap='viridis')

        # Draw modern grid with subtle lines
        for i in range(grid_size + 1):
            self.ax.axhline(i - 0.5, color=self.grid_color, linewidth=0.8, alpha=0.6)
            self.ax.axvline(i - 0.5, color=self.grid_color, linewidth=0.8, alpha=0.6)
        
        # Draw modern obstacles with 3D effect
        for x in range(grid_size):
            for y in range(grid_size):
                if self.grid_world.grid[x, y] == 1:  # Obstacle
                    # Main obstacle body
                    rect = patches.Rectangle((y-0.45, grid_size-x-1.45), 0.9, 0.9,
                                           linewidth=2, edgecolor='#654321',
                                           facecolor=self.obstacle_color, alpha=0.9)
                    self.ax.add_patch(rect)
                    # Add 3D shadow effect
                    shadow = patches.Rectangle((y-0.4, grid_size-x-1.4), 0.8, 0.8,
                                             linewidth=0, facecolor='#2d1810', alpha=0.5)
                    self.ax.add_patch(shadow)

        # Draw modern goals with glowing effect
        for goal_id, (gx, gy) in enumerate(self.grid_world.goal_positions):
            goal_color = self.goal_colors[goal_id % len(self.goal_colors)]

            if goal_id not in self.grid_world.visited_goals:
                # Unvisited goal - glowing effect
                # Outer glow
                outer_glow = patches.Circle((gy, grid_size-gx-1), 0.4,
                                          linewidth=0, facecolor=goal_color, alpha=0.3)
                self.ax.add_patch(outer_glow)
                # Main goal
                circle = patches.Circle((gy, grid_size-gx-1), 0.3,
                                      linewidth=3, edgecolor='white',
                                      facecolor=goal_color, alpha=0.9)
                self.ax.add_patch(circle)
                # Goal number with modern font
                self.ax.text(gy, grid_size-gx-1, str(goal_id+1), ha='center', va='center',
                           fontsize=10, color='white', fontweight='bold',
                           family='monospace')
            else:
                # Visited goal - checkmark effect
                circle = patches.Circle((gy, grid_size-gx-1), 0.25,
                                      linewidth=2, edgecolor='white',
                                      facecolor='#228b22', alpha=0.8)
                self.ax.add_patch(circle)
                # Checkmark symbol
                self.ax.text(gy, grid_size-gx-1, '✓', ha='center', va='center',
                           fontsize=12, color='white', fontweight='bold')
        
        # Draw modern robot paths with glow effect
        if show_paths:
            for robot_id, path in enumerate(self.grid_world.robot_paths):
                if len(path) > 1:
                    path_x = [grid_size - pos[0] - 1 for pos in path]
                    path_y = [pos[1] for pos in path]
                    robot_color = self.robot_colors[robot_id % len(self.robot_colors)]

                    # Draw path with glow effect
                    # Outer glow
                    self.ax.plot(path_y, path_x, '-',
                               color=robot_color, alpha=0.3, linewidth=6)
                    # Main path
                    self.ax.plot(path_y, path_x, '-',
                               color=robot_color, alpha=self.path_alpha, linewidth=3)
                    # Inner highlight
                    self.ax.plot(path_y, path_x, '-',
                               color='white', alpha=0.6, linewidth=1)
        
        # Draw modern futuristic robots
        for robot_id, (rx, ry) in enumerate(self.grid_world.robot_positions):
            robot_color = self.robot_colors[robot_id % len(self.robot_colors)]
            robot_x = ry
            robot_y = grid_size - rx - 1

            # Robot glow effect (outer aura)
            glow = patches.Circle((robot_x, robot_y), 0.45,
                                linewidth=0, facecolor=robot_color, alpha=0.2)
            self.ax.add_patch(glow)

            # Main robot body (hexagonal shape for futuristic look)
            hexagon_points = []
            for i in range(6):
                angle = i * np.pi / 3
                x = robot_x + 0.25 * np.cos(angle)
                y = robot_y + 0.25 * np.sin(angle)
                hexagon_points.append([x, y])

            hexagon = patches.Polygon(hexagon_points, closed=True,
                                    linewidth=3, edgecolor='white',
                                    facecolor=robot_color, alpha=0.9)
            self.ax.add_patch(hexagon)

            # Inner core (smaller circle)
            core = patches.Circle((robot_x, robot_y), 0.15,
                                linewidth=2, edgecolor='white',
                                facecolor='#ffffff', alpha=0.8)
            self.ax.add_patch(core)

            # Robot sensors (small dots around the hexagon)
            for i in range(6):
                angle = i * np.pi / 3
                sensor_x = robot_x + 0.3 * np.cos(angle)
                sensor_y = robot_y + 0.3 * np.sin(angle)
                sensor = patches.Circle((sensor_x, sensor_y), 0.04,
                                      linewidth=1, edgecolor='white',
                                      facecolor='#ffff00', alpha=0.9)
                self.ax.add_patch(sensor)

            # Robot ID with modern styling
            self.ax.text(robot_x, robot_y, f'R{robot_id}', ha='center', va='center',
                        fontsize=9, fontweight='bold', color='black',
                        family='monospace')

            # Add directional indicator (small arrow)
            arrow = patches.FancyArrowPatch((robot_x, robot_y + 0.1),
                                          (robot_x, robot_y + 0.2),
                                          arrowstyle='->', mutation_scale=15,
                                          color='white', alpha=0.8, linewidth=2)
            self.ax.add_patch(arrow)
        
        # Draw modern task assignments with animated lines
        if show_assignments:
            for robot_id, goal_id in self.grid_world.task_assignments.items():
                if goal_id is not None and goal_id not in self.grid_world.visited_goals:
                    rx, ry = self.grid_world.robot_positions[robot_id]
                    gx, gy = self.grid_world.goal_positions[goal_id]
                    robot_color = self.robot_colors[robot_id % len(self.robot_colors)]

                    # Draw assignment line with pulse effect
                    self.ax.plot([ry, gy], [grid_size-rx-1, grid_size-gx-1],
                               linestyle='--', color=robot_color, alpha=0.8, linewidth=3)
                    # Add glow effect
                    self.ax.plot([ry, gy], [grid_size-rx-1, grid_size-gx-1],
                               linestyle='-', color='white', alpha=0.4, linewidth=1)
        
        # Add task information overlay if provided
        if task_info is not None:
            self._add_task_info_overlay(task_info, step_count, grid_size)

        # Add modern border effect
        border = patches.Rectangle((-0.5, -0.5), grid_size, grid_size,
                                 linewidth=4, edgecolor='#00d4ff',
                                 facecolor='none', alpha=0.8)
        self.ax.add_patch(border)

        # Set axis properties with modern styling
        self.ax.set_xlim(-0.5, grid_size - 0.5)
        self.ax.set_ylim(-0.5, grid_size - 0.5)
        self.ax.set_aspect('equal')

        # Remove axis ticks for cleaner look
        self.ax.set_xticks([])
        self.ax.set_yticks([])

        # Modern title with gradient effect
        title = f'🤖 Multi-Robot Grid World - Step {step_count} - Goals: {len(self.grid_world.visited_goals)}/{self.grid_world.num_goals}'
        if task_info and 'model_name' in task_info:
            title = f"🎯 {task_info['model_name']} - {title}"
        self.ax.set_title(title, color=self.text_color, fontsize=16, fontweight='bold',
                         family='monospace', pad=20)

        # Modern legend with futuristic styling
        legend_elements = []
        for i in range(self.grid_world.num_robots):
            color = self.robot_colors[i % len(self.robot_colors)]
            legend_elements.append(patches.Patch(color=color, label=f'🤖 Robot {i}'))

        # Add goal legend with modern colors
        legend_elements.append(patches.Patch(color=self.goal_colors[0], label='🎯 Active Goal'))
        legend_elements.append(patches.Patch(color='#228b22', label='✅ Completed Goal'))
        legend_elements.append(patches.Patch(color=self.obstacle_color, label='🚧 Obstacle'))

        legend = self.ax.legend(handles=legend_elements, loc='upper left',
                              bbox_to_anchor=(1.02, 1), fontsize=10)
        legend.get_frame().set_facecolor('#1a1a1a')
        legend.get_frame().set_edgecolor(self.grid_color)
        legend.get_frame().set_alpha(0.9)
        for text in legend.get_texts():
            text.set_color(self.text_color)
            text.set_family('monospace')

        plt.tight_layout()
        plt.draw()
        plt.pause(0.1)

    def _add_task_info_overlay(self, task_info: dict, step_count: int, grid_size: int):
        """Add task information overlay directly on the grid."""
        try:
            # Position for text overlay (bottom area of the grid)
            text_y_start = -0.4
            text_x_start = 0
            line_height = 0.15

            # Prepare information lines
            info_lines = []

            # Task Status
            if 'total_tasks' in task_info and 'completed_tasks' in task_info:
                completed = len(task_info['completed_tasks']) if isinstance(task_info['completed_tasks'], list) else task_info['completed_tasks']
                total = task_info['total_tasks']
                remaining_tasks = task_info.get('remaining_tasks', [])
                info_lines.append(f"TASKS: {completed}/{total} completed")
                if remaining_tasks:
                    remaining_str = str(remaining_tasks)[:30] + "..." if len(str(remaining_tasks)) > 30 else str(remaining_tasks)
                    info_lines.append(f"   Remaining: {remaining_str}")

            # Robot Assignments
            if 'robot_assignments' in task_info:
                info_lines.append("ROBOT ASSIGNMENTS:")
                for robot_id, tasks in task_info['robot_assignments'].items():
                    task_str = str(tasks)[:25] + "..." if len(str(tasks)) > 25 else str(tasks)
                    info_lines.append(f"   R{robot_id}: {task_str} ({len(tasks)})")

            # Performance Metrics
            if 'workload_balance' in task_info or 'makespan' in task_info:
                info_lines.append("METRICS:")

                if 'workload_balance' in task_info:
                    balance = task_info['workload_balance']
                    balance_status = "GOOD" if balance < 1.0 else "POOR"
                    info_lines.append(f"   Balance: {balance:.3f} ({balance_status})")

                if 'makespan' in task_info and task_info['makespan'] > 0:
                    makespan = task_info['makespan']
                    min_makespan = task_info.get('min_makespan', 0)
                    makespan_status = "GOOD" if makespan <= min_makespan * 1.2 else "HIGH"
                    info_lines.append(f"   Makespan: {makespan:.1f} ({makespan_status})")
                    if min_makespan > 0:
                        info_lines.append(f"   Min: {min_makespan:.1f}")

            # Model Parameters
            if 'alpha' in task_info and 'beta' in task_info:
                alpha, beta = task_info['alpha'], task_info['beta']
                info_lines.append(f"MODEL: a:{alpha:.1f} b:{beta:.1f}")

            # Display all lines with modern styling
            for i, line in enumerate(info_lines):
                y_pos = text_y_start - (i * line_height)

                # Modern background with gradient effect
                bbox_props = dict(boxstyle="round,pad=0.4",
                                facecolor="#1a1a1a", alpha=0.9,
                                edgecolor="#00d4ff", linewidth=1.5)

                self.ax.text(text_x_start, y_pos, line,
                           fontsize=10, fontfamily='monospace',
                           color='#ffffff', fontweight='bold',
                           verticalalignment='top', horizontalalignment='left',
                           bbox=bbox_props)

            # Modern step counter with neon effect
            step_text = f"⚡ Step {step_count}"
            self.ax.text(grid_size - 0.1, grid_size - 0.1, step_text,
                        fontsize=14, fontweight='bold', color='#00ff00',
                        verticalalignment='top', horizontalalignment='right',
                        bbox=dict(boxstyle="round,pad=0.3",
                                facecolor="#1a1a1a", alpha=0.9,
                                edgecolor="#00ff00", linewidth=2))

        except Exception as e:
            # Silently handle any display errors
            pass

    def save_frame(self, filename: str):
        """Save current frame as image."""
        plt.savefig(filename, dpi=150, bbox_inches='tight')


def create_grid_from_scheduling_problem(prefix: str, grid_size: int = 8) -> GridWorld:
    """
    Create a GridWorld from a scheduling problem instance.

    Args:
        prefix: Path prefix to the problem instance files
        grid_size: Size of the grid world

    Returns:
        GridWorld instance with robots and goals positioned based on the problem
    """
    try:
        # Load problem data
        dur_file = f"{prefix}_dur.txt"
        loc_file = f"{prefix}_loc.txt"

        if not os.path.exists(dur_file) or not os.path.exists(loc_file):
            raise FileNotFoundError(f"Problem files not found: {prefix}")

        # Load durations and locations
        durations = np.loadtxt(dur_file, dtype=np.float32)
        locations = np.loadtxt(loc_file, dtype=np.int32)

        # Determine number of tasks and robots
        num_tasks = durations.shape[0]
        num_robots = durations.shape[1]

        # Create grid world
        grid_world = GridWorld(grid_size=grid_size, num_robots=num_robots, num_goals=num_tasks)

        # Override goal positions with problem locations (scaled to fit grid)
        max_coord = max(np.max(locations[:, 0]), np.max(locations[:, 1])) if len(locations) > 0 else 1
        scale_factor = (grid_size - 2) / max(max_coord, 1)  # Leave border

        grid_world.goal_positions = []
        for i in range(num_tasks):
            if i < len(locations):
                # Scale and center the locations
                scaled_x = int(locations[i, 0] * scale_factor) + 1
                scaled_y = int(locations[i, 1] * scale_factor) + 1
                # Ensure within bounds
                scaled_x = max(1, min(grid_size - 2, scaled_x))
                scaled_y = max(1, min(grid_size - 2, scaled_y))
                grid_world.goal_positions.append((scaled_x, scaled_y))
            else:
                # Random position for extra goals
                x, y = random.randint(1, grid_size-2), random.randint(1, grid_size-2)
                grid_world.goal_positions.append((x, y))

        # Place goals on grid
        grid_world.grid.fill(0)  # Clear grid
        for goal_id, (gx, gy) in enumerate(grid_world.goal_positions):
            if grid_world.grid[gx, gy] == 0:  # Only if empty
                grid_world.grid[gx, gy] = 2  # Goal

        # Place robots at corners or edges
        robot_start_positions = [
            (1, 1),  # Top-left
            (grid_size-2, grid_size-2),  # Bottom-right
            (1, grid_size-2),  # Top-right
            (grid_size-2, 1),  # Bottom-left
        ]

        grid_world.robot_positions = []
        for robot_id in range(num_robots):
            if robot_id < len(robot_start_positions):
                pos = robot_start_positions[robot_id]
            else:
                # Random edge position for extra robots
                edge = random.choice(['top', 'bottom', 'left', 'right'])
                if edge == 'top':
                    pos = (1, random.randint(1, grid_size-2))
                elif edge == 'bottom':
                    pos = (grid_size-2, random.randint(1, grid_size-2))
                elif edge == 'left':
                    pos = (random.randint(1, grid_size-2), 1)
                else:  # right
                    pos = (random.randint(1, grid_size-2), grid_size-2)

            # Ensure position is not occupied by goal
            x, y = pos
            while grid_world.grid[x, y] != 0:
                x = (x + 1) % (grid_size - 1) + 1
                y = (y + 1) % (grid_size - 1) + 1

            grid_world.robot_positions.append((x, y))
            grid_world.grid[x, y] = 3  # Robot
            grid_world.robot_paths[robot_id] = [(x, y)]

        # Initialize tracking
        grid_world.visited_goals = set()
        grid_world.completed_tasks = set()  # Add this attribute
        grid_world.task_assignments = {}

        return grid_world

    except Exception as e:
        print(f"Error creating grid from scheduling problem: {e}")
        # Fallback: create default grid world
        return GridWorld(grid_size=grid_size, num_robots=2, num_goals=5)


def create_scheduling_problem_from_grid(grid_world: GridWorld) -> Dict:
    """
    Convert grid world state to scheduling problem format compatible with your models.
    
    Args:
        grid_world: Current grid world state
        
    Returns:
        Dictionary with problem data in your model's expected format
    """
    num_robots = grid_world.num_robots
    unvisited_goals = [i for i in range(grid_world.num_goals) if i not in grid_world.visited_goals]
    num_tasks = len(unvisited_goals)
    
    if num_tasks == 0:
        return None
    
    # Create duration matrix (Manhattan distance + base cost)
    durations = np.zeros((num_tasks, num_robots), dtype=np.float32)
    
    for task_idx, goal_id in enumerate(unvisited_goals):
        goal_pos = grid_world.goal_positions[goal_id]
        for robot_id in range(num_robots):
            robot_pos = grid_world.robot_positions[robot_id]
            # Manhattan distance as duration
            distance = abs(goal_pos[0] - robot_pos[0]) + abs(goal_pos[1] - robot_pos[1])
            durations[task_idx, robot_id] = distance + 1  # +1 base execution cost
    
    # Create locations (goal positions)
    locations = np.array([grid_world.goal_positions[goal_id] for goal_id in unvisited_goals], dtype=np.int32)
    
    # Create deadlines (can be set based on problem requirements)
    deadlines = np.array([[i+1, num_tasks * 2] for i in range(num_tasks)], dtype=np.int32)

    # Create wait constraints (format: [ti, tj, wait_time])
    # For grid world, we'll use minimal wait constraints
    if num_tasks >= 2:
        wait_constraints = np.array([[1, 2, 0]], dtype=np.int32)  # Minimal constraint between tasks 1 and 2
    else:
        wait_constraints = np.array([[1, 1, 0]], dtype=np.int32)  # Self-constraint for single task
    
    return {
        'durations': durations,
        'locations': locations,
        'deadlines': deadlines,
        'wait_constraints': wait_constraints,
        'unvisited_goals': unvisited_goals,
        'num_tasks': num_tasks,
        'num_robots': num_robots
    }
