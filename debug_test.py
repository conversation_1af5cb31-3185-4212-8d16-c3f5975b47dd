#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
debug_test.py

Simple diagnostic script to debug the simulation issues.
"""

import os
import sys
import torch
import numpy as np

def test_basic_imports():
    """Test basic imports."""
    print("🔍 Testing basic imports...")
    try:
        import torch
        print(f"✓ PyTorch version: {torch.__version__}")
        
        import numpy as np
        print(f"✓ NumPy version: {np.__version__}")
        
        from utils import SchedulingEnv
        print("✓ SchedulingEnv imported")
        
        from hetnet import MultiRobotDecentralizedSystem
        print("✓ MultiRobotDecentralizedSystem imported")
        
        from multi_objective_utils import calculate_workload_balance
        print("✓ calculate_workload_balance imported")
        
        return True
    except Exception as e:
        print(f"❌ Import error: {e}")
        return False

def test_model_loading():
    """Test model loading."""
    print("\n🔍 Testing model loading...")
    try:
        model_path = "./cp_decentralized/decentralized_checkpoint_00050.tar"
        if not os.path.exists(model_path):
            print(f"❌ Model file not found: {model_path}")
            return False
        
        print(f"✓ Model file exists: {model_path}")
        
        device = torch.device("cpu")
        checkpoint = torch.load(model_path, map_location=device)
        
        print(f"✓ Checkpoint loaded")
        print(f"  Keys: {list(checkpoint.keys())}")
        print(f"  Num robots: {checkpoint.get('num_robots', 'Not found')}")
        print(f"  Alpha: {checkpoint.get('alpha', 'Not found')}")
        print(f"  Beta: {checkpoint.get('beta', 'Not found')}")
        print(f"  Step: {checkpoint.get('step', 'Not found')}")
        
        return True
    except Exception as e:
        print(f"❌ Model loading error: {e}")
        return False

def test_problem_instance():
    """Test loading a problem instance."""
    print("\n🔍 Testing problem instance loading...")
    try:
        from utils import SchedulingEnv
        
        test_data_path = "./problem_instances/constraints"
        if not os.path.exists(test_data_path):
            print(f"❌ Test data path not found: {test_data_path}")
            return False
        
        print(f"✓ Test data path exists: {test_data_path}")
        
        # Try to load first instance
        prefix = os.path.join(test_data_path, "00001")
        
        required_files = [f"{prefix}_dur.txt", f"{prefix}_ddl.txt", f"{prefix}_loc.txt", f"{prefix}_wait.txt"]
        for file_path in required_files:
            if os.path.exists(file_path):
                print(f"✓ Found: {os.path.basename(file_path)}")
            else:
                print(f"❌ Missing: {os.path.basename(file_path)}")
                return False
        
        # Try to create environment
        env = SchedulingEnv(prefix)
        print(f"✓ SchedulingEnv created")
        print(f"  Tasks: {env.dur.shape[0]}")
        print(f"  Robots: {env.dur.shape[1]}")
        print(f"  Locations: {env.loc.shape}")
        
        # Test consistency
        ok, mm = env.check_consistency_makespan(updateDG=False)
        print(f"  Consistency: {ok}, Min makespan: {mm}")
        
        return True
    except Exception as e:
        print(f"❌ Problem instance error: {e}")
        return False

def test_simple_solve():
    """Test simple solving without simulation."""
    print("\n🔍 Testing simple solve...")
    try:
        from utils import SchedulingEnv
        
        prefix = "./problem_instances/constraints/00001"
        env = SchedulingEnv(prefix)
        env.set_multi_objective_params(alpha=0.5, beta=0.5)
        
        ok, mm = env.check_consistency_makespan(updateDG=False)
        if not ok:
            print(f"❌ Instance 00001 is infeasible")
            return False
        
        print(f"✓ Instance 00001 is feasible, min makespan: {mm}")
        
        # Test getting unscheduled tasks
        unsch_tasks = env.get_unscheduled_tasks()
        print(f"✓ Unscheduled tasks: {unsch_tasks}")
        
        # Test inserting a task
        if len(unsch_tasks) > 0:
            task = unsch_tasks[0]
            robot = 0
            success, reward, done = env.insert_robot(task, robot)
            print(f"✓ Insert task {task} to robot {robot}: success={success}, reward={reward:.3f}, done={done}")
        
        return True
    except Exception as e:
        print(f"❌ Simple solve error: {e}")
        return False

def main():
    """Run all diagnostic tests."""
    print("🔧 DIAGNOSTIC TEST SUITE")
    print("="*50)
    
    tests = [
        ("Basic Imports", test_basic_imports),
        ("Model Loading", test_model_loading),
        ("Problem Instance", test_problem_instance),
        ("Simple Solve", test_simple_solve)
    ]
    
    results = {}
    for test_name, test_func in tests:
        try:
            results[test_name] = test_func()
        except Exception as e:
            print(f"❌ {test_name} failed with exception: {e}")
            results[test_name] = False
    
    print("\n" + "="*50)
    print("🔧 DIAGNOSTIC RESULTS")
    print("="*50)
    
    for test_name, result in results.items():
        status = "✓ PASS" if result else "❌ FAIL"
        print(f"{test_name}: {status}")
    
    all_passed = all(results.values())
    print(f"\nOverall: {'✓ ALL TESTS PASSED' if all_passed else '❌ SOME TESTS FAILED'}")
    
    if all_passed:
        print("\n🎉 Environment is ready for simulation testing!")
    else:
        print("\n⚠️ Please fix the failing tests before running simulation.")

if __name__ == "__main__":
    main()
