#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
working_simulation_test.py

Working simulation test that combines the proven logic from test_models_csv_format.py
with MiniGrid visualization and balanced workload focus.
"""

import os
import argparse
import time
import numpy as np
import pandas as pd
import torch
import matplotlib.pyplot as plt
from typing import Dict, List, Tuple

print("🔍 Starting imports...")
from utils import SchedulingEnv, build_hetgraph, hetgraph_node_helper
from hetnet import DecentralizedScheduleNet, MultiRobotDecentralizedSystem
from multi_objective_utils import calculate_workload_balance
print("✓ Core imports successful")

try:
    from minigrid_environment import GridWorld, GridWorldVisualizer, create_grid_from_scheduling_problem
    SIMULATION_AVAILABLE = True
    print("✓ MiniGrid simulation available")
except Exception as e:
    SIMULATION_AVAILABLE = False
    print(f"⚠️ MiniGrid simulation not available: {e}")


def solve_instance_with_working_logic(prefix: str, num_tasks: int, num_robots: int,
                                    decentralized_system, device: torch.device,
                                    alpha: float = 0.5, beta: float = 0.5,
                                    show_simulation: bool = False) -> Tuple[float, float, Dict, float, bool]:
    """Solve using the EXACT working logic from test_models_csv_format.py."""
    
    print(f"🔍 Solving instance: {os.path.basename(prefix)}")
    t0 = time.time()

    try:
        env = SchedulingEnv(prefix)
        env.set_multi_objective_params(alpha=alpha, beta=beta)
        ok, mm = env.check_consistency_makespan(updateDG=False)
        if not ok:
            print(f"❌ Instance is infeasible")
            return float("nan"), float("nan"), {}, time.time() - t0, False
        env.min_makespan = mm
        print(f"✓ Instance loaded, min makespan: {mm}")
    except Exception as e:
        print(f"❌ Error loading environment: {e}")
        return float("nan"), float("nan"), {}, time.time() - t0, False

    assignments = {r: [] for r in range(num_robots)}
    feasible_flag = True

    # Calculate map_width dynamically
    max_coord = max(np.max(env.loc[:, 0]), np.max(env.loc[:, 1])) if len(env.loc) > 0 else 6
    map_width = max(6, max_coord + 2)
    loc_dist_threshold = max(1, map_width // 4)
    
    print(f"📊 Map width: {map_width}, Loc threshold: {loc_dist_threshold}")

    # Initialize simulation if available and requested
    grid_world = None
    visualizer = None
    if show_simulation and SIMULATION_AVAILABLE:
        try:
            grid_world = create_grid_from_scheduling_problem(prefix, 8)
            plt.ion()
            visualizer = GridWorldVisualizer(grid_world)
            visualizer.render(title=f"Working Simulation - {os.path.basename(prefix)}")
            plt.show()
            time.sleep(1)
            print("✓ Simulation initialized")
        except Exception as e:
            print(f"⚠️ Simulation setup failed: {e}")
            show_simulation = False

    step_count = 0
    print(f"🚀 Starting decision-making loop...")
    
    while True:
        unsch_tasks = env.get_unscheduled_tasks()
        if len(unsch_tasks) == 0:
            print("✅ All tasks completed!")
            break

        step_count += 1
        if step_count > num_tasks * 2:
            print(f"⚠️ Safety limit reached at step {step_count}")
            feasible_flag = False
            break

        print(f"\n📋 Step {step_count}: {len(unsch_tasks)} tasks remaining: {unsch_tasks}")

        # Phase 1: Each robot makes local decisions (EXACT same as test_models_csv_format.py)
        robot_decisions = {}
        robot_confidences = {}

        for robot_id in range(num_robots):
            try:
                print(f"  🤖 Robot {robot_id} evaluating...")
                
                # Build graph
                g = build_hetgraph(
                    env.halfDG,
                    num_tasks,
                    num_robots,
                    env.dur.astype(np.float32),
                    map_width,
                    np.array(env.loc, dtype=np.int64),
                    loc_dist_threshold,
                    env.partials,
                    np.array(unsch_tasks, dtype=np.int64),
                    robot_id,
                    np.array(unsch_tasks, dtype=np.int64)
                ).to(device)

                # Build features
                feat_dict = hetgraph_node_helper(
                    env.halfDG.number_of_nodes(),
                    env.partialw,
                    env.partials,
                    env.loc,
                    env.dur,
                    map_width,
                    num_robots,
                    len(unsch_tasks)
                )

                feat_tensors = {k: torch.tensor(v, device=device, dtype=torch.float32)
                               for k, v in feat_dict.items()}

                # Forward pass
                with torch.no_grad():
                    outputs = decentralized_system.forward_with_communication(
                        robot_id, g, feat_tensors, communication_rounds=2
                    )
                    q_values = outputs['value'].cpu().numpy().reshape(-1)
                    confidence = outputs['confidence'].cpu().numpy().reshape(-1)

                # Find best task
                best_idx = np.argmax(q_values)
                best_task = int(unsch_tasks[best_idx])
                best_q = float(q_values[best_idx])
                best_conf = float(confidence[best_idx])

                robot_decisions[robot_id] = {
                    'task': best_task,
                    'q_value': best_q,
                    'confidence': best_conf
                }
                robot_confidences[robot_id] = best_conf
                
                print(f"    Task {best_task}: Q={best_q:.3f}, Conf={best_conf:.3f}")

            except Exception as e:
                print(f"    ❌ Error: {e}")
                robot_decisions[robot_id] = {
                    'task': None,
                    'q_value': -float('inf'),
                    'confidence': 0.0
                }
                robot_confidences[robot_id] = 0.0

        # Phase 2: Conflict resolution (EXACT same as test_models_csv_format.py)
        best_robot = None
        best_task = None
        best_confidence = -1.0

        for robot_id, decision in robot_decisions.items():
            if (decision['task'] is not None and
                decision['confidence'] > best_confidence):
                best_confidence = decision['confidence']
                best_robot = robot_id
                best_task = decision['task']

        # Fallback
        if best_robot is None:
            best_q = -float('inf')
            for robot_id, decision in robot_decisions.items():
                if (decision['task'] is not None and
                    decision['q_value'] > best_q):
                    best_q = decision['q_value']
                    best_robot = robot_id
                    best_task = decision['task']

        if best_robot is None or best_task is None:
            print("❌ No valid decision found")
            feasible_flag = False
            break

        # Show decision
        current_workload = [len(assignments[r]) for r in range(num_robots)]
        print(f"  📊 Current workload: {current_workload}")
        print(f"  🎯 Selected: Robot {best_robot} → Task {best_task} (conf: {best_confidence:.3f})")

        # Execute action
        success, _, done_flag = env.insert_robot(best_task, best_robot)
        if not success:
            print("❌ Failed to execute action")
            feasible_flag = False
            break

        assignments[best_robot].append(best_task)
        print(f"  ✅ Task {best_task} assigned to Robot {best_robot}")

        # Update simulation
        if show_simulation and grid_world is not None and visualizer is not None:
            try:
                task_idx = best_task - 1
                if task_idx < len(grid_world.goal_positions):
                    grid_world.completed_tasks.add(task_idx)
                    grid_world.visited_goals.add(task_idx)
                    updated_workload = [len(assignments[r]) for r in range(num_robots)]
                    visualizer.render(title=f"Step {step_count}: Robot {best_robot} → Task {best_task}, Workload: {updated_workload}")
                    time.sleep(0.8)
            except Exception as e:
                print(f"⚠️ Simulation update failed: {e}")

        if done_flag:
            print("🏁 All tasks scheduled!")
            break

    runtime = time.time() - t0

    # Final simulation update
    if show_simulation and visualizer is not None:
        try:
            final_workload = [len(assignments[r]) for r in range(num_robots)]
            visualizer.render(title=f"COMPLETED - Workload: {final_workload}")
            time.sleep(2)
            plt.ioff()
        except Exception as e:
            pass

    if not feasible_flag:
        print("❌ Solution not feasible")
        return float("nan"), float("nan"), assignments, runtime, False

    # Calculate final metrics
    try:
        ok_final, final_makespan = env.check_consistency_makespan(updateDG=False)
        if not ok_final:
            print("❌ Final consistency check failed")
            return float("nan"), float("nan"), assignments, runtime, False

        workload_balance = calculate_workload_balance(assignments, num_robots)
        
        print(f"✅ Solution found!")
        print(f"   Makespan: {final_makespan:.2f}")
        print(f"   Workload balance: {workload_balance:.3f}")
        print(f"   Runtime: {runtime:.2f}s")
        print(f"   Assignments: {assignments}")

        return final_makespan, workload_balance, assignments, runtime, True

    except Exception as e:
        print(f"❌ Error calculating final metrics: {e}")
        return float("nan"), float("nan"), assignments, runtime, False


def main():
    parser = argparse.ArgumentParser(description="Working Simulation Test")
    parser.add_argument("--model-path", required=True, help="Path to trained decentralized model")
    parser.add_argument("--test-data", required=True, help="Path to test data directory")
    parser.add_argument("--num-tasks", type=int, default=5, help="Number of tasks")
    parser.add_argument("--max-instances", type=int, default=5, help="Max instances to test")
    parser.add_argument("--device", default="cpu", help="Device")
    parser.add_argument("--show-simulation", action="store_true", help="Show simulation")
    
    args = parser.parse_args()
    
    print("🚀 WORKING SIMULATION TEST")
    print("="*50)
    print(f"Model: {args.model_path}")
    print(f"Test data: {args.test_data}")
    print(f"Max instances: {args.max_instances}")
    print(f"Show simulation: {args.show_simulation}")
    
    # Load model
    print("\n🔍 Loading model...")
    device = torch.device(args.device)
    checkpoint = torch.load(args.model_path, map_location=device)
    
    num_robots = checkpoint['num_robots']
    alpha = checkpoint.get('alpha', 0.5)
    beta = checkpoint.get('beta', 0.5)
    step = checkpoint.get('step', 0)
    
    # Create decentralized system
    in_dim = {'task': 6, 'loc': 1, 'robot': 1, 'state': 4, 'value': 1}
    hid_dim = {'task': 64, 'loc': 64, 'robot': 64, 'state': 64, 'value': 64}
    out_dim = {'task': 32, 'loc': 32, 'robot': 32, 'state': 32, 'value': 1}
    cetypes = [
        ('task', 'temporal', 'task'),
        ('task', 'located_in', 'loc'), ('loc', 'near', 'loc'),
        ('task', 'assigned_to', 'robot'), ('robot', 'com', 'robot'),
        ('task', 'tin', 'state'), ('loc', 'lin', 'state'),
        ('robot', 'rin', 'state'), ('state', 'sin', 'state'),
        ('task', 'tto', 'value'), ('robot', 'rto', 'value'),
        ('state', 'sto', 'value'), ('value', 'vto', 'value'),
        ('task', 'take_time', 'robot'), ('robot', 'use_time', 'task')
    ]
    
    decentralized_system = MultiRobotDecentralizedSystem(
        in_dim, hid_dim, out_dim, cetypes, num_robots, 8
    ).to(device)
    
    # Load robot networks
    robot_networks = checkpoint['robot_networks']
    for robot_id in range(num_robots):
        robot_key = f'robot_{robot_id}'
        if robot_key in robot_networks:
            robot_net = decentralized_system.get_robot_network(robot_id)
            robot_net.load_state_dict(robot_networks[robot_key])
            robot_net.eval()
    
    print(f"✓ Model loaded: α={alpha:.3f}, β={beta:.3f}, step={step}, robots={num_robots}")
    
    # Test instances
    results = {'successful': 0, 'total': 0, 'makespans': [], 'workload_balances': [], 'runtimes': []}
    
    print(f"\n🔍 Testing instances...")
    for inst_id in range(1, args.max_instances + 1):
        prefix = os.path.join(args.test_data, f"{inst_id:05d}")
        
        if not os.path.isfile(f"{prefix}_dur.txt"):
            continue
        
        results['total'] += 1
        print(f"\n{'='*50}")
        print(f"[Instance {inst_id:05d}]")
        
        makespan, balance, assigns, runtime, success = solve_instance_with_working_logic(
            prefix, args.num_tasks, num_robots, decentralized_system, device, 
            alpha, beta, args.show_simulation
        )
        
        if success:
            results['successful'] += 1
            results['makespans'].append(makespan)
            results['workload_balances'].append(balance)
            results['runtimes'].append(runtime)
            print(f"✅ SUCCESS")
        else:
            print(f"❌ FAILED")
    
    # Summary
    print(f"\n{'='*50}")
    print("📊 FINAL RESULTS")
    print(f"{'='*50}")
    print(f"Total instances: {results['total']}")
    print(f"Successful: {results['successful']}")
    print(f"Success rate: {results['successful']/results['total']*100:.1f}%")
    
    if results['successful'] > 0:
        print(f"Average makespan: {np.mean(results['makespans']):.3f}")
        print(f"Average workload balance: {np.mean(results['workload_balances']):.3f}")
        print(f"Average runtime: {np.mean(results['runtimes']):.3f}s")


if __name__ == "__main__":
    main()
