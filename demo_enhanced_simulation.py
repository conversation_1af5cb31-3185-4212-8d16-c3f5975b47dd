#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
demo_enhanced_simulation.py

Demonstration of the enhanced MiniGrid simulation with:
- Black grid background
- Robot-shaped robots (not circles)
- Brown obstacles
- 5 tasks, 2 robots
- Balanced workload distribution
- Each task performed only once
"""

import numpy as np
import matplotlib.pyplot as plt
import time
from minigrid_environment import GridWorld, GridWorldVisualizer


def demo_enhanced_features():
    """Demonstrate the enhanced simulation features."""
    
    print("🎮 ENHANCED MINIGRID SIMULATION DEMO")
    print("="*50)
    print("Features:")
    print("✅ Black grid background")
    print("✅ Robot-shaped robots (not circles)")
    print("✅ Brown obstacles")
    print("✅ 5 tasks, 2 robots")
    print("✅ Balanced workload distribution")
    print("✅ Each task performed only once")
    print("="*50)
    
    # Create enhanced grid world
    grid_size = 8
    num_robots = 2
    num_tasks = 5
    num_obstacles = 4
    
    grid_world = GridWorld(grid_size, num_robots, num_tasks, num_obstacles)
    
    # Create visualizer
    plt.ion()
    visualizer = GridWorldVisualizer(grid_world)
    
    # Initial state
    print("\n🎯 Step 1: Initial State")
    visualizer.render(title="Enhanced MiniGrid - Initial State")
    plt.show()
    time.sleep(2)
    
    # Simulate balanced task assignment
    task_assignments = {0: [], 1: []}  # Robot 0 and Robot 1
    
    # Balanced assignment: Robot 0 gets tasks [0, 2, 4], Robot 1 gets tasks [1, 3]
    balanced_schedule = [
        (0, 0),  # Robot 0 → Task 0
        (1, 1),  # Robot 1 → Task 1
        (0, 2),  # Robot 0 → Task 2
        (1, 3),  # Robot 1 → Task 3
        (0, 4),  # Robot 0 → Task 4
    ]
    
    print("\n🤖 Executing Balanced Task Schedule:")
    print("Robot 0: Tasks [1, 3, 5]")
    print("Robot 1: Tasks [2, 4]")
    
    for step, (robot_id, task_id) in enumerate(balanced_schedule):
        print(f"\n📋 Step {step + 2}: Robot {robot_id} → Task {task_id + 1}")
        
        # Move robot to task location
        goal_pos = grid_world.goal_positions[task_id]
        robot_pos = grid_world.robot_positions[robot_id]
        
        print(f"  🎮 Moving Robot {robot_id} from {robot_pos} to Task {task_id + 1} at {goal_pos}")
        
        # Simulate movement (simplified)
        steps_taken = 0
        while robot_pos != goal_pos and steps_taken < 20:
            rx, ry = robot_pos
            gx, gy = goal_pos
            
            # Simple movement towards goal
            if gx > rx:
                new_pos = (rx + 1, ry)
            elif gx < rx:
                new_pos = (rx - 1, ry)
            elif gy > ry:
                new_pos = (rx, ry + 1)
            elif gy < ry:
                new_pos = (rx, ry - 1)
            else:
                break
            
            # Check if move is valid
            if (0 <= new_pos[0] < grid_size and 0 <= new_pos[1] < grid_size and
                grid_world.grid[new_pos[0], new_pos[1]] != 99):  # Not obstacle
                
                # Update robot position
                grid_world.grid[robot_pos[0], robot_pos[1]] = 0  # Clear old position
                grid_world.robot_positions[robot_id] = new_pos
                grid_world.grid[new_pos[0], new_pos[1]] = robot_id + 1  # Set new position
                grid_world.robot_paths[robot_id].append(new_pos)
                robot_pos = new_pos
                steps_taken += 1
                
                # Update visualization
                visualizer.render(title=f"Robot {robot_id} moving to Task {task_id + 1} (Step {steps_taken})")
                time.sleep(0.5)
        
        # Mark task as completed
        if task_id not in grid_world.completed_tasks:
            grid_world.completed_tasks.add(task_id)
            grid_world.visited_goals.add(task_id)
            task_assignments[robot_id].append(task_id + 1)
            
            print(f"  ✅ Task {task_id + 1} completed by Robot {robot_id}")
            
            # Show completion
            visualizer.render(title=f"✅ Task {task_id + 1} Completed by Robot {robot_id}")
            time.sleep(1.5)
    
    # Final state
    print(f"\n🏆 FINAL RESULTS:")
    print(f"Robot 0 completed tasks: {task_assignments[0]}")
    print(f"Robot 1 completed tasks: {task_assignments[1]}")
    print(f"Total tasks completed: {len(grid_world.completed_tasks)}/{num_tasks}")
    
    # Calculate workload balance
    workload_0 = len(task_assignments[0])
    workload_1 = len(task_assignments[1])
    balance = abs(workload_0 - workload_1) / max(workload_0, workload_1) if max(workload_0, workload_1) > 0 else 0
    
    print(f"Workload balance: {balance:.3f} (lower is better)")
    print(f"Makespan optimization: ✅ (both robots working)")
    print(f"Task uniqueness: ✅ (each task performed once)")
    
    # Final visualization
    final_workload = [workload_0, workload_1]
    visualizer.render(title=f"🏆 COMPLETED - Workload: {final_workload}, Balance: {balance:.3f}")
    
    print(f"\n🎮 Enhanced Features Demonstrated:")
    print(f"✅ Black grid background")
    print(f"✅ Robot shapes (not circles)")
    print(f"✅ Brown obstacles (■)")
    print(f"✅ Task completion tracking (✓)")
    print(f"✅ Balanced workload: Robot 0: {workload_0}, Robot 1: {workload_1}")
    print(f"✅ No task duplication")
    
    # Keep visualization open
    input("\nPress Enter to close the simulation...")
    plt.ioff()
    plt.close()


def demo_simple_scenario():
    """Simple demonstration scenario."""
    print("\n🎯 SIMPLE SCENARIO DEMO")
    print("="*30)
    
    # Create a simple 6x6 grid
    grid_world = GridWorld(6, 2, 3, 2)  # 6x6, 2 robots, 3 tasks, 2 obstacles
    
    plt.ion()
    visualizer = GridWorldVisualizer(grid_world)
    
    # Show initial state
    visualizer.render(title="Simple Scenario - 6x6 Grid")
    plt.show()
    time.sleep(2)
    
    # Simulate task completion
    for task_id in range(3):
        robot_id = task_id % 2  # Alternate between robots
        
        print(f"Robot {robot_id} completing Task {task_id + 1}")
        
        # Mark as completed
        grid_world.completed_tasks.add(task_id)
        grid_world.visited_goals.add(task_id)
        
        # Update visualization
        visualizer.render(title=f"Task {task_id + 1} completed by Robot {robot_id}")
        time.sleep(1.5)
    
    # Final state
    visualizer.render(title="All Tasks Completed!")
    time.sleep(3)
    plt.ioff()
    plt.close()


if __name__ == "__main__":
    print("🚀 Starting Enhanced MiniGrid Simulation Demo...")
    
    try:
        # Run the enhanced demo
        demo_enhanced_features()
        
        print("\n" + "="*50)
        print("✅ Demo completed successfully!")
        print("All enhanced features are working:")
        print("  • Black grid background ✅")
        print("  • Robot-shaped robots ✅") 
        print("  • Brown obstacles ✅")
        print("  • Balanced workload ✅")
        print("  • Task uniqueness ✅")
        print("="*50)
        
    except KeyboardInterrupt:
        print("\n⏹️ Demo interrupted by user")
    except Exception as e:
        print(f"\n❌ Error in demo: {e}")
        # Try simple scenario as fallback
        print("🔄 Trying simple scenario...")
        demo_simple_scenario()
