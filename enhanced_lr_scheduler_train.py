#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
enhanced_lr_scheduler_train.py

Enhanced version of lr_scheduler_train.py using the new 8-layer model
and advanced loss functions for achieving loss values below 0.01.
"""

import os
import sys
import argparse
import time
import copy
import pickle
import numpy as np
import torch
import torch.nn.functional as F
import torch.nn.utils as utils
from torch.optim.lr_scheduler import ReduceLROnPlateau
import matplotlib.pyplot as plt

from hetnet import ScheduleNet8Layer  # Use the new 8-layer model
from utils import ReplayMemory, Transition, action_helper_rollout
from utils import SchedulingEnv, hetgraph_node_helper, build_hetgraph
from advanced_loss_functions import MultiObjectiveLoss, ProgressiveLossScaler


class EnhancedRewardStabilizer:
    """Enhanced reward stabilizer with better normalization"""
    def __init__(self, window_size=100, clip_percentile=95, alpha=0.05):
        self.window_size = window_size
        self.clip_percentile = clip_percentile
        self.alpha = alpha
        self.reward_buffer = []
        self.running_mean = 0.0
        self.running_std = 1.0
        self.running_var = 1.0
        
    def get_smoothed_reward(self, rewards):
        """Get smoothed and normalized rewards"""
        if isinstance(rewards, (int, float)):
            rewards = [rewards]
            
        # Add to buffer
        self.reward_buffer.extend(rewards)
        if len(self.reward_buffer) > self.window_size:
            self.reward_buffer = self.reward_buffer[-self.window_size:]
        
        # Update running statistics
        if len(self.reward_buffer) > 10:
            current_mean = np.mean(self.reward_buffer)
            current_var = np.var(self.reward_buffer)
            
            # Exponential moving average
            self.running_mean = (1 - self.alpha) * self.running_mean + self.alpha * current_mean
            self.running_var = (1 - self.alpha) * self.running_var + self.alpha * current_var
            self.running_std = np.sqrt(self.running_var)
        
        # Normalize and clip rewards
        smoothed_rewards = []
        for reward in rewards:
            if self.running_std > 0:
                # Z-score normalization
                normalized = (reward - self.running_mean) / (self.running_std + 1e-8)
                # Clip to reasonable range
                clipped = np.clip(normalized, -3, 3)
                smoothed_rewards.append(clipped)
            else:
                smoothed_rewards.append(reward)
                
        return smoothed_rewards


def fill_demo_data(folder, start_no, end_no, gamma_d):
    """Fill demonstration data - same as original but with enhanced logging"""
    memory = ReplayMemory(100000)
    total_no = end_no - start_no + 1
    gurobi_count = 0
    
    print(f"Loading demonstration data from {folder}...")
    print(f"Processing instances {start_no} to {end_no} ({total_no} total)")
    
    for graph_no in range(start_no, end_no+1):
        if graph_no % 50 == 0:
            print(f'Loading.. {graph_no}/{end_no} ({gurobi_count} feasible found)')
        
        fname = folder + '/%05d' % graph_no
        env = SchedulingEnv(fname)

        # check if the graph is feasible for Gurobi
        solname = folder + 'v9/%05d' % graph_no
        solname_w = solname +'_w.txt'
        
        if os.path.isfile(solname_w):
            gurobi_count += 1
            
            optimals = []
            for i in range(env.num_robots):
                if os.path.isfile(solname+'_%d.txt' % i):
                    optimals.append(np.loadtxt(solname+'_%d.txt' % i, dtype=np.int32))
                else:
                    optimals.append([])
                    
            optimalw = np.loadtxt(solname_w, dtype=np.int32)
        else:
            continue
    
        # Generate transitions of the problem
        state_graphs = []
        partials = []
        partialw = []
        actions_task = []
        actions_robot = []
        rewards = []
        terminates = []
        
        state_graphs.append(copy.deepcopy(env.halfDG))
        partials.append(copy.deepcopy(env.partials))
        partialw.append(copy.deepcopy(env.partialw))
        terminates.append(False)
        
        rj=0
        for i in range(env.num_tasks):
            for j in range(env.num_robots):
                if optimalw[i] in optimals[j]:
                    rj = j
                    break
            
            act_chosen = optimalw[i]
            
            # insert the node, update state, and get reward
            _, reward, done = env.insert_robot(act_chosen, rj)
            
            state_graphs.append(copy.deepcopy(env.halfDG))
            partials.append(copy.deepcopy(env.partials))
            partialw.append(copy.deepcopy(env.partialw))
            actions_task.append(act_chosen)
            actions_robot.append(rj)
            rewards.append(reward)
            terminates.append(done)
    
        # Save transitions into memory buffer
        for t in range(env.num_tasks):
            curr_g = copy.deepcopy(state_graphs[t])
            curr_partials = copy.deepcopy(partials[t])
            curr_partialw = copy.deepcopy(partialw[t])
            act_task = actions_task[t]
            act_robot = actions_robot[t]
            
            # calculate discounted reward
            reward_n = 0.0
            for j in range(t, env.num_tasks):
                reward_n += (gamma_d**(j-t)) * rewards[j]
            
            next_g = copy.deepcopy(state_graphs[t+1])
            next_partials = copy.deepcopy(partials[t+1])
            next_partialw = copy.deepcopy(partialw[t+1])
            next_done = terminates[t+1]
            
            locs = copy.deepcopy(env.loc)
            durs = copy.deepcopy(env.dur)
            
            memory.push(curr_g, curr_partials, curr_partialw,
                        locs, durs,
                        act_task, act_robot,
                        reward_n, next_g, next_partials, 
                        next_partialw, next_done)
    
    print(f'Gurobi feasible found: {gurobi_count}/{total_no}')
    print(f'Memory buffer size: {len(memory)}')
    return memory


if __name__ == '__main__':
    parser = argparse.ArgumentParser(description='Enhanced SSAN Training with 8-Layer Model')
    parser.add_argument('--cpu', default=False, action='store_true')
    parser.add_argument('--path-to-train', default='./gen/r2t20_001', type=str)
    parser.add_argument('--num-robots', default=2, type=int)
    parser.add_argument('--train-start-no', default=1, type=int)
    parser.add_argument('--train-end-no', default=100, type=int)  # Smaller for testing
    parser.add_argument('--steps', default=2000, type=int)
    parser.add_argument('--gamma', default=0.99, type=float)
    parser.add_argument('--batch-size', default=16, type=int)
    parser.add_argument('--lr', default=1e-4, type=float)
    parser.add_argument('--weight-decay', default=5e-7, type=float)
    parser.add_argument('--resume-training', default=False, action='store_true')
    parser.add_argument('--path-to-checkpoint', default='./enhanced_cp/checkpoint_01000.tar', type=str)
    parser.add_argument('--load-memory', default=False, action='store_true')
    parser.add_argument('--path-to-replay-buffer', default='./buffer/buffer_enhanced.pkl', type=str)
    parser.add_argument('--checkpoint-interval', default=50, type=int)
    parser.add_argument('--save-replay-buffer-to', default=None, type=str)
    parser.add_argument('--cpsave', default='./enhanced_cp', type=str)
    parser.add_argument('--target-loss', default=0.005, type=float)
    parser.add_argument('--use-advanced-loss', default=True, action='store_true')
    parser.add_argument('--dropout', default=0.1, type=float)
    
    args = parser.parse_args()

    resume_training = args.resume_training
    load_memory = args.load_memory
        
    GAMMA = args.gamma
    BATCH_SIZE = args.batch_size
    total_steps = args.steps
    
    # Enhanced tracking
    loss_history = []
    component_losses_history = {'mse': [], 'focal': [], 'huber': [], 'regularization': []}
    q_pred_history = []
    q_target_history = []
    reward_history = []
    reward_running_mean = []
    reward_running_std = []
    lr_history = []
    loss_scale_history = []
    
    device = torch.device("cpu")

    # Enhanced model dimensions
    in_dim = {'task': 6, 'loc': 1, 'robot': 1, 'state': 4, 'value': 1}
    hid_dim = {'task': 96, 'loc': 96, 'robot': 96, 'state': 96, 'value': 96}  # Larger
    out_dim = {'task': 48, 'loc': 48, 'robot': 48, 'state': 48, 'value': 1}  # Larger

    cetypes = [('task', 'temporal', 'task'),
               ('task', 'located_in', 'loc'), ('loc', 'near', 'loc'),
               ('task', 'assigned_to', 'robot'), ('robot', 'com', 'robot'),
               ('task', 'tin', 'state'), ('loc', 'lin', 'state'),
               ('robot', 'rin', 'state'), ('state', 'sin', 'state'),
               ('task', 'tto', 'value'), ('robot', 'rto', 'value'),
               ('state', 'sto', 'value'), ('value', 'vto', 'value'),
               ('task', 'take_time', 'robot'), ('robot', 'use_time', 'task')]
    
    num_heads = 8
    num_robots = args.num_robots
    map_width = 6
    loc_dist_threshold = 1
    
    # Create enhanced 8-layer model
    print("Creating enhanced 8-layer model...")
    policy_net = ScheduleNet8Layer(
        in_dim, hid_dim, out_dim, cetypes, 
        num_heads=num_heads, dropout=args.dropout
    ).to(device)
    
    print(f"Model parameters: {sum(p.numel() for p in policy_net.parameters()):,}")

    # Enhanced optimizer
    optimizer = torch.optim.AdamW(
        policy_net.parameters(), 
        lr=args.lr, 
        weight_decay=args.weight_decay,
        betas=(0.9, 0.999)
    )

    # Enhanced learning rate scheduler
    lr_scheduler = ReduceLROnPlateau(
        optimizer, 'min', factor=0.5, patience=30, min_lr=1e-8, verbose=True
    )
    
    # Advanced loss function
    if args.use_advanced_loss:
        criterion = MultiObjectiveLoss(
            loss_weights={'mse': 0.4, 'focal': 0.3, 'huber': 0.2, 'regularization': 0.1},
            adaptive_weighting=True
        )
        loss_scaler = ProgressiveLossScaler(
            initial_scale=1.0,
            target_loss=args.target_loss,
            decay_rate=0.998
        )
        print("Using advanced multi-objective loss function")
    else:
        criterion = None
        loss_scaler = None
        print("Using standard MSE loss function")
    
    # Load checkpoint if resuming
    if resume_training:
        trained_checkpoint = args.path_to_checkpoint
        cp = torch.load(trained_checkpoint)
        policy_net.load_state_dict(cp['policy_net_state_dict'])
        optimizer.load_state_dict(cp['optimizer_state_dict'])
        training_steps_done = cp['training_steps']
        start_step = training_steps_done + 1
        print(f"Resumed training from step {start_step}")
    else:
        start_step = 1
        training_steps_done = 0
    
    # Load or create memory
    if load_memory:
        bname = args.path_to_replay_buffer
        with open(bname, 'rb') as f:
            memory = pickle.load(f)
        print(f'Memory loaded, length: {len(memory)}')
    else:
        folder = args.path_to_train
        start_no = args.train_start_no
        end_no = args.train_end_no
        memory = fill_demo_data(folder, start_no, end_no, GAMMA)
    
    print('Initialization done')

    # Ensure checkpoint directory exists
    os.makedirs(args.cpsave, exist_ok=True)
    print(f'Checkpoints will be saved to: {args.cpsave}')

    # Enhanced reward stabilizer
    reward_stabilizer = EnhancedRewardStabilizer(window_size=150, clip_percentile=95, alpha=0.03)

    print('Starting enhanced training...')
    print(f'Model: 8-Layer ScheduleNet with {sum(p.numel() for p in policy_net.parameters()):,} parameters')
    print(f'Learning rate: {args.lr}, Batch size: {BATCH_SIZE}')
    print(f'Target loss: {args.target_loss}')
    print(f'Advanced loss: {"Enabled" if args.use_advanced_loss else "Disabled"}')

    best_loss = float('inf')
    consecutive_improvements = 0

    for i_step in range(start_step, total_steps+1):
        start_t = time.time()
        policy_net.train()
        
        transitions = memory.sample(BATCH_SIZE)
        batch = Transition(*zip(*transitions))
        loss = torch.tensor(0.0).to(device)

        # Track metrics
        batch_q_preds = []
        batch_q_targets = []
        batch_rewards_raw = []
        batch_rewards_stabilized = []
        component_losses_batch = {'mse': 0, 'focal': 0, 'huber': 0, 'regularization': 0}

        # First pass: collect all raw rewards for stabilization
        for i in range(BATCH_SIZE):
            raw_reward = batch.reward_n[i]
            batch_rewards_raw.append(raw_reward)

        # Apply enhanced reward stabilization
        batch_rewards_stabilized = reward_stabilizer.get_smoothed_reward(batch_rewards_raw)

        # Second pass: process samples with stabilized rewards
        for i in range(BATCH_SIZE):
            num_tasks = batch.curr_g[i].number_of_nodes() - 2
            unsch_tasks = np.array(action_helper_rollout(num_tasks, batch.curr_partialw[i]),
                                   dtype=np.int64)

            g = build_hetgraph(batch.curr_g[i], num_tasks, num_robots, batch.durs[i],
                               map_width, np.array(batch.locs[i], dtype=np.int64),
                               loc_dist_threshold, batch.curr_partials[i], unsch_tasks,
                               batch.act_robot[i], unsch_tasks)
            g = g.to(device)

            num_actions = len(unsch_tasks)
            feat_dict = hetgraph_node_helper(batch.curr_g[i].number_of_nodes(),
                                             batch.curr_partialw[i],
                                             batch.curr_partials[i],
                                             batch.locs[i], batch.durs[i],
                                             map_width, num_robots, num_actions)

            feat_dict_tensor = {}
            for key in feat_dict:
                feat_dict_tensor[key] = torch.Tensor(feat_dict[key]).to(device)

            outputs = policy_net(g, feat_dict_tensor)
            q_pre = outputs['value']

            # Enhanced reward processing
            stabilized_reward = batch_rewards_stabilized[i]
            normalized_reward = stabilized_reward / 50.0  # More aggressive normalization

            # Calculate targets with smaller offset for better convergence
            if num_actions > 1:
                offset = 0.003  # Even smaller offset for sub-0.01 loss
                target_list = np.full((num_actions, 1),
                                      normalized_reward - offset, dtype=np.float32)

                LfD_weights = np.full((num_actions, 1),
                                      0.9/(num_actions-1), dtype=np.float32)

                q_s_a_alt_target1 = q_pre.clone().detach()
                q_s_a_alt_target2 = torch.tensor(target_list).to(device)
                q_s_a_alt_target = torch.min(q_s_a_alt_target1, q_s_a_alt_target2)

                # q value for expert action
                expert_idx = 0
                for j in range(num_actions):
                    if unsch_tasks[j] == batch.act_task[i]:
                        expert_idx = j
                        break
                q_s_a_alt_target[expert_idx, 0] = normalized_reward
                LfD_weights[expert_idx, 0] = 1.0
            else:
                target_list = np.full((1, 1), normalized_reward, dtype=np.float32)
                LfD_weights = np.full((1, 1), 1.0, dtype=np.float32)
                q_s_a_alt_target = torch.tensor(target_list).to(device)

            # Store predictions and targets for logging
            batch_q_preds.append(q_pre.detach().cpu().numpy())
            batch_q_targets.append(q_s_a_alt_target.detach().cpu().numpy())

            # Calculate loss using advanced loss function
            if args.use_advanced_loss and criterion is not None:
                sample_loss, sample_component_losses = criterion(
                    q_pre, q_s_a_alt_target, policy_net.parameters()
                )
                
                # Apply progressive loss scaling
                current_loss_value = sample_loss.item()
                scale = loss_scaler.update_scale(current_loss_value)
                scaled_loss = loss_scaler.scale_loss(sample_loss)
                
                loss += scaled_loss / BATCH_SIZE
                
                # Track component losses
                for key, value in sample_component_losses.items():
                    component_losses_batch[key] += value.item()
            else:
                # Standard MSE loss
                loss_SL = F.mse_loss(q_pre, q_s_a_alt_target, reduction='none')
                LfD_weights = torch.Tensor(LfD_weights).to(device)
                loss_SL = loss_SL * LfD_weights
                loss += loss_SL.sum() / BATCH_SIZE

        loss_batch = loss.data.cpu().numpy()

        # Enhanced gradient handling
        optimizer.zero_grad()
        loss.backward()
        grad_norm = utils.clip_grad_norm_(policy_net.parameters(), max_norm=1.0)
        optimizer.step()

        # Update learning rate scheduler
        if i_step > 1:
            lr_scheduler.step(loss_batch)

        # Calculate enhanced metrics
        avg_q_pred = np.mean([np.mean(q) for q in batch_q_preds])
        avg_q_target = np.mean([np.mean(q) for q in batch_q_targets])
        avg_reward_raw = np.mean(batch_rewards_raw)
        avg_reward_stabilized = np.mean(batch_rewards_stabilized)
        pred_diff = abs(avg_q_pred - avg_q_target)
        current_lr = optimizer.param_groups[0]['lr']

        # Store enhanced metrics
        loss_history.append(loss_batch)
        q_pred_history.append(avg_q_pred)
        q_target_history.append(avg_q_target)
        reward_history.append(avg_reward_stabilized)
        reward_running_mean.append(reward_stabilizer.running_mean)
        reward_running_std.append(reward_stabilizer.running_std)
        lr_history.append(current_lr)
        
        if args.use_advanced_loss:
            for key in component_losses_batch:
                component_losses_history[key].append(component_losses_batch[key] / BATCH_SIZE)
            loss_scale_history.append(loss_scaler.scale)

        # Track best loss
        if loss_batch < best_loss:
            best_loss = loss_batch
            consecutive_improvements += 1
        else:
            consecutive_improvements = 0

        end_t = time.time()

        # Enhanced logging
        if i_step % 10 == 0 or loss_batch < args.target_loss:
            log_msg = f'[Step {i_step:4d}] Loss: {loss_batch:.6f} (Best: {best_loss:.6f}), '
            log_msg += f'Q_pred: {avg_q_pred:.4f}, Q_target: {avg_q_target:.4f}, '
            log_msg += f'Diff: {pred_diff:.4f}, R_stab: {avg_reward_stabilized:.3f}, '
            log_msg += f'LR: {current_lr:.2e}'
            
            if args.use_advanced_loss:
                log_msg += f', Scale: {loss_scaler.scale:.3f}'
            
            log_msg += f', Time: {end_t - start_t:.2f}s'
            print(log_msg)

        # Enhanced early stopping
        if loss_batch < args.target_loss:
            print(f'Target loss {args.target_loss} achieved at step {i_step}!')
            if consecutive_improvements >= 3:  # Ensure stability
                print('Training completed successfully!')
                break

        # Save enhanced checkpoints
        if i_step % args.checkpoint_interval == 0:
            checkpoint_path = args.cpsave + '/enhanced_checkpoint_{:05d}.tar'.format(i_step)
            checkpoint_data = {
                'training_steps': i_step,
                'policy_net_state_dict': policy_net.state_dict(),
                'optimizer_state_dict': optimizer.state_dict(),
                'loss': loss_batch,
                'best_loss': best_loss,
                'loss_history': loss_history,
                'q_pred_history': q_pred_history,
                'q_target_history': q_target_history,
                'reward_history': reward_history,
                'reward_running_mean': reward_running_mean,
                'reward_running_std': reward_running_std,
                'lr_history': lr_history,
                'component_losses_history': component_losses_history,
                'loss_scale_history': loss_scale_history,
                'args': args
            }
            torch.save(checkpoint_data, checkpoint_path)
            print(f'Enhanced checkpoint saved: {checkpoint_path}')

    # Final results
    print(f'\nEnhanced training completed!')
    print(f'Final loss: {loss_history[-1]:.6f}')
    print(f'Best loss achieved: {best_loss:.6f}')
    print(f'Target loss: {args.target_loss}')
    print(f'Target achieved: {"Yes" if best_loss < args.target_loss else "No"}')

    # Save final model
    final_model_path = args.cpsave + '/enhanced_final_model.tar'
    torch.save({
        'model_state_dict': policy_net.state_dict(),
        'loss_history': loss_history,
        'best_loss': best_loss,
        'args': args
    }, final_model_path)
    print(f'Final model saved: {final_model_path}')

    # Save replay buffer if requested
    if args.save_replay_buffer_to is not None:
        with open(args.save_replay_buffer_to, 'wb') as f:
            pickle.dump(memory, f)
        print(f'Replay buffer saved: {args.save_replay_buffer_to}')
