#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
minigrid_environment.py

MiniGrid environment for multi-robot task scheduling simulation.
Creates a grid world where robots move to complete tasks at different locations.
"""

import numpy as np
import matplotlib.pyplot as plt
import matplotlib.patches as patches
from typing import Dict, List, Tuple, Optional
import random


class GridWorld:
    """
    Grid world environment for multi-robot task scheduling with black background and obstacles.
    """

    def __init__(self, grid_size: int = 10, num_robots: int = 2, num_goals: int = 5, num_obstacles: int = 8):
        self.grid_size = grid_size
        self.num_robots = num_robots
        self.num_goals = num_goals
        self.num_obstacles = num_obstacles

        # Initialize grid (0=empty, >0=robot_id, <0=goal_id, 99=obstacle)
        self.grid = np.zeros((grid_size, grid_size), dtype=int)

        # Robot positions (x, y)
        self.robot_positions = []
        self.robot_paths = [[] for _ in range(num_robots)]

        # Goal positions and states
        self.goal_positions = []
        self.visited_goals = set()
        self.completed_tasks = set()  # Track completed tasks to avoid duplication
        self.task_assignments = {}  # robot_id -> goal_id

        # Obstacle positions
        self.obstacle_positions = []

        # Initialize positions
        self._initialize_positions()

        print(f"GridWorld initialized: {grid_size}x{grid_size}, {num_robots} robots, {num_goals} goals, {num_obstacles} obstacles")
    
    def _initialize_positions(self):
        """Initialize robot, goal, and obstacle positions."""
        # Place obstacles first
        for i in range(self.num_obstacles):
            while True:
                x, y = random.randint(1, self.grid_size-2), random.randint(1, self.grid_size-2)
                if self.grid[x, y] == 0:  # Empty cell
                    self.obstacle_positions.append((x, y))
                    self.grid[x, y] = 99  # Obstacle marker
                    break

        # Place robots at corners to avoid obstacles
        robot_start_positions = [
            (0, 0), (0, self.grid_size-1), (self.grid_size-1, 0), (self.grid_size-1, self.grid_size-1)
        ]

        for i in range(self.num_robots):
            if i < len(robot_start_positions):
                pos = robot_start_positions[i]
            else:
                # Find empty position for additional robots
                while True:
                    x, y = random.randint(0, self.grid_size-1), random.randint(0, self.grid_size-1)
                    if self.grid[x, y] == 0:
                        pos = (x, y)
                        break

            self.robot_positions.append(pos)
            self.grid[pos[0], pos[1]] = i + 1  # Robot ID (1-based)

        # Place goals randomly, avoiding obstacles and robots
        for i in range(self.num_goals):
            while True:
                x, y = random.randint(0, self.grid_size-1), random.randint(0, self.grid_size-1)
                if self.grid[x, y] == 0:  # Empty cell
                    self.goal_positions.append((x, y))
                    self.grid[x, y] = -(i + 1)  # Goal ID (negative for distinction)
                    break
    
    def move_robot(self, robot_id: int, action: int) -> bool:
        """
        Move robot in the grid.
        Actions: 0=stay, 1=up, 2=right, 3=down, 4=left
        Returns True if move was successful.
        """
        if robot_id >= self.num_robots:
            return False
        
        current_pos = self.robot_positions[robot_id]
        new_pos = current_pos
        
        # Calculate new position based on action
        if action == 1:  # Up
            new_pos = (current_pos[0] - 1, current_pos[1])
        elif action == 2:  # Right
            new_pos = (current_pos[0], current_pos[1] + 1)
        elif action == 3:  # Down
            new_pos = (current_pos[0] + 1, current_pos[1])
        elif action == 4:  # Left
            new_pos = (current_pos[0], current_pos[1] - 1)
        # action == 0 means stay in place
        
        # Check bounds
        if (new_pos[0] < 0 or new_pos[0] >= self.grid_size or 
            new_pos[1] < 0 or new_pos[1] >= self.grid_size):
            return False
        
        # Check if new position is occupied by another robot or obstacle
        cell_value = self.grid[new_pos[0], new_pos[1]]
        if (cell_value > 0 and new_pos != current_pos) or cell_value == 99:  # Robot or obstacle
            return False
        
        # Update grid
        if new_pos != current_pos:
            # Clear old position (but keep goal if it was there)
            old_cell_value = self.grid[current_pos[0], current_pos[1]]
            if old_cell_value == robot_id + 1:  # Only robot was there
                self.grid[current_pos[0], current_pos[1]] = 0
            
            # Set new position
            goal_at_new_pos = self.grid[new_pos[0], new_pos[1]]
            self.grid[new_pos[0], new_pos[1]] = robot_id + 1
            
            # Check if robot reached a goal
            if goal_at_new_pos < 0:  # Negative means goal
                goal_id = abs(goal_at_new_pos) - 1
                if goal_id not in self.visited_goals and goal_id not in self.completed_tasks:
                    self.visited_goals.add(goal_id)
                    self.completed_tasks.add(goal_id)
                    print(f"🎯 Robot {robot_id} completed task {goal_id} at {new_pos}")
            
            # Update robot position
            self.robot_positions[robot_id] = new_pos
            self.robot_paths[robot_id].append(new_pos)
        
        return True
    
    def assign_task(self, robot_id: int, goal_id: int):
        """Assign a goal to a robot."""
        self.task_assignments[robot_id] = goal_id
    
    def get_state(self) -> Dict:
        """Get current state of the grid world."""
        return {
            'robot_positions': self.robot_positions.copy(),
            'goal_positions': self.goal_positions.copy(),
            'visited_goals': self.visited_goals.copy(),
            'task_assignments': self.task_assignments.copy(),
            'completion_rate': len(self.visited_goals) / self.num_goals,
            'grid': self.grid.copy()
        }
    
    def is_complete(self) -> bool:
        """Check if all goals have been visited."""
        return len(self.visited_goals) >= self.num_goals
    
    def reset(self):
        """Reset the grid world to initial state."""
        self.grid = np.zeros((self.grid_size, self.grid_size), dtype=int)
        self.robot_positions = []
        self.robot_paths = [[] for _ in range(self.num_robots)]
        self.goal_positions = []
        self.visited_goals = set()
        self.task_assignments = {}
        self._initialize_positions()


class GridWorldVisualizer:
    """
    Enhanced visualizer for the grid world environment with black background and robot shapes.
    """

    def __init__(self, grid_world: GridWorld):
        self.grid_world = grid_world
        self.fig, self.ax = plt.subplots(figsize=(12, 12))
        self.fig.patch.set_facecolor('black')  # Black figure background
        self.robot_colors = ['#FF4444', '#4444FF', '#44FF44', '#FFAA44', '#FF44FF']  # Bright colors
        self.goal_colors = ['#FFB6C1', '#ADD8E6', '#90EE90', '#F0E68C', '#DDA0DD']  # Light colors
        self.obstacle_color = '#8B4513'  # Brown color for obstacles
    
    def render(self, show_paths: bool = True, title: str = "Multi-Robot Task Scheduling"):
        """Render the current state of the grid world with black background."""
        self.ax.clear()
        self.ax.set_xlim(-0.5, self.grid_world.grid_size - 0.5)
        self.ax.set_ylim(-0.5, self.grid_world.grid_size - 0.5)
        self.ax.set_aspect('equal')
        self.ax.set_facecolor('black')  # Black background
        self.ax.set_title(title, color='white', fontsize=16, fontweight='bold')

        # Draw grid with white lines
        for i in range(self.grid_world.grid_size + 1):
            self.ax.axhline(i - 0.5, color='white', linewidth=1, alpha=0.3)
            self.ax.axvline(i - 0.5, color='white', linewidth=1, alpha=0.3)

        # Draw obstacles first (brown squares)
        for obs_x, obs_y in self.grid_world.obstacle_positions:
            obstacle_patch = patches.Rectangle((obs_y - 0.45, obs_x - 0.45), 0.9, 0.9,
                                             linewidth=2, edgecolor='black',
                                             facecolor=self.obstacle_color, alpha=0.8)
            self.ax.add_patch(obstacle_patch)
            self.ax.text(obs_y, obs_x, '■', ha='center', va='center', fontsize=16, color='white')
        
        # Draw goals (tasks)
        for goal_id, (gx, gy) in enumerate(self.grid_world.goal_positions):
            color = self.goal_colors[goal_id % len(self.goal_colors)]
            if goal_id in self.grid_world.completed_tasks:
                # Completed task - green checkmark
                goal_patch = patches.Rectangle((gy - 0.4, gx - 0.4), 0.8, 0.8,
                                             linewidth=3, edgecolor='lime',
                                             facecolor='darkgreen', alpha=0.8)
                self.ax.add_patch(goal_patch)
                self.ax.text(gy, gx, '✓', ha='center', va='center', fontsize=24,
                           color='lime', fontweight='bold')
            else:
                # Pending task
                goal_patch = patches.Rectangle((gy - 0.4, gx - 0.4), 0.8, 0.8,
                                             linewidth=2, edgecolor='white',
                                             facecolor=color, alpha=0.9)
                self.ax.add_patch(goal_patch)
                self.ax.text(gy, gx, f'T{goal_id+1}', ha='center', va='center',
                           fontweight='bold', color='black', fontsize=12)
        
        # Draw robot paths
        if show_paths:
            for robot_id, path in enumerate(self.grid_world.robot_paths):
                if len(path) > 1:
                    path_x = [p[1] for p in path]
                    path_y = [p[0] for p in path]
                    color = self.robot_colors[robot_id % len(self.robot_colors)]
                    self.ax.plot(path_x, path_y, color=color, alpha=0.7, linewidth=3, linestyle='-')

        # Draw robots as robot shapes
        for robot_id, (rx, ry) in enumerate(self.grid_world.robot_positions):
            color = self.robot_colors[robot_id % len(self.robot_colors)]

            # Robot body (rectangle)
            robot_body = patches.Rectangle((ry - 0.35, rx - 0.25), 0.7, 0.5,
                                         linewidth=3, edgecolor='white',
                                         facecolor=color, alpha=0.9)
            self.ax.add_patch(robot_body)

            # Robot head (smaller rectangle on top)
            robot_head = patches.Rectangle((ry - 0.15, rx - 0.35), 0.3, 0.2,
                                         linewidth=2, edgecolor='white',
                                         facecolor=color, alpha=0.9)
            self.ax.add_patch(robot_head)

            # Robot eyes
            self.ax.plot([ry - 0.08, ry + 0.08], [rx - 0.28, rx - 0.28], 'wo', markersize=4)

            # Robot ID
            self.ax.text(ry, rx, f'R{robot_id}', ha='center', va='center',
                        fontweight='bold', color='white', fontsize=10)
        
        # Add legend with white text
        legend_elements = []
        for i in range(self.grid_world.num_robots):
            color = self.robot_colors[i % len(self.robot_colors)]
            legend_elements.append(patches.Patch(color=color, label=f'Robot {i}'))

        for i in range(self.grid_world.num_goals):
            if i in self.grid_world.completed_tasks:
                legend_elements.append(patches.Patch(color='darkgreen', label=f'Task {i+1} ✓'))
            else:
                color = self.goal_colors[i % len(self.goal_colors)]
                legend_elements.append(patches.Patch(color=color, label=f'Task {i+1}'))

        legend_elements.append(patches.Patch(color=self.obstacle_color, label='Obstacles'))

        legend = self.ax.legend(handles=legend_elements, loc='upper left', bbox_to_anchor=(1, 1),
                               facecolor='black', edgecolor='white')
        for text in legend.get_texts():
            text.set_color('white')

        # Add status text with white background for visibility
        completed_tasks = len(self.grid_world.completed_tasks)
        status_text = f"Tasks Completed: {completed_tasks}/{self.grid_world.num_goals}\n"
        status_text += f"Progress: {completed_tasks/self.grid_world.num_goals*100:.1f}%\n"
        status_text += f"Robots: {self.grid_world.num_robots}"
        self.ax.text(0.02, 0.98, status_text, transform=self.ax.transAxes,
                    verticalalignment='top', bbox=dict(boxstyle="round,pad=0.5",
                    facecolor="white", alpha=0.9, edgecolor='black'),
                    fontsize=12, fontweight='bold')
        
        plt.tight_layout()
        plt.draw()
        plt.pause(0.1)
    
    def save_frame(self, filename: str):
        """Save current frame as image."""
        plt.savefig(filename, dpi=150, bbox_inches='tight')


def create_scheduling_problem_from_grid(grid_world: GridWorld) -> Optional[Dict]:
    """
    Convert grid world state to scheduling problem format.
    This creates a scheduling problem based on current robot positions and unvisited goals.
    """
    unvisited_goals = []
    for goal_id in range(grid_world.num_goals):
        if goal_id not in grid_world.visited_goals:
            unvisited_goals.append(goal_id)
    
    if len(unvisited_goals) == 0:
        return None
    
    num_tasks = len(unvisited_goals)
    num_robots = grid_world.num_robots
    
    # Create duration matrix (distance-based)
    durations = np.zeros((num_tasks, num_robots), dtype=int)
    locations = []
    
    for task_idx, goal_id in enumerate(unvisited_goals):
        goal_pos = grid_world.goal_positions[goal_id]
        locations.append(goal_pos)
        
        for robot_id in range(num_robots):
            robot_pos = grid_world.robot_positions[robot_id]
            # Manhattan distance + base task duration
            distance = abs(goal_pos[0] - robot_pos[0]) + abs(goal_pos[1] - robot_pos[1])
            durations[task_idx, robot_id] = max(1, distance + random.randint(1, 3))
    
    # Create simple deadlines (generous)
    deadlines = np.full(num_tasks, 50, dtype=int)
    
    # Create wait constraints (minimal)
    wait_constraints = np.zeros((num_tasks, num_tasks), dtype=int)
    
    return {
        'num_tasks': num_tasks,
        'num_robots': num_robots,
        'durations': durations,
        'deadlines': deadlines,
        'wait_constraints': wait_constraints,
        'locations': np.array(locations),
        'unvisited_goals': unvisited_goals
    }


def create_grid_from_scheduling_problem(problem_prefix: str, grid_size: int = 10) -> GridWorld:
    """
    Create an enhanced grid world from a scheduling problem instance with better positioning.
    """
    try:
        # Load problem data
        durations = np.loadtxt(f"{problem_prefix}_dur.txt", dtype=int)
        locations = np.loadtxt(f"{problem_prefix}_loc.txt", dtype=int)

        num_tasks, num_robots = durations.shape

        # Create grid world with fewer obstacles for better navigation
        grid_world = GridWorld(grid_size, num_robots, num_tasks, num_obstacles=4)

        # Clear default positions
        grid_world.grid = np.zeros((grid_size, grid_size), dtype=int)
        grid_world.robot_positions = []
        grid_world.goal_positions = []
        grid_world.obstacle_positions = []
        grid_world.visited_goals = set()
        grid_world.completed_tasks = set()

        # Place obstacles first (fewer and more spread out)
        obstacle_positions = [
            (grid_size//3, grid_size//3),
            (2*grid_size//3, grid_size//3),
            (grid_size//3, 2*grid_size//3),
            (2*grid_size//3, 2*grid_size//3)
        ]

        for i, pos in enumerate(obstacle_positions[:4]):  # Only 4 obstacles
            if 0 < pos[0] < grid_size-1 and 0 < pos[1] < grid_size-1:
                grid_world.obstacle_positions.append(pos)
                grid_world.grid[pos[0], pos[1]] = 99

        # Place robots at opposite corners for better distribution
        robot_start_positions = [
            (0, 0), (grid_size-1, grid_size-1), (0, grid_size-1), (grid_size-1, 0)
        ]

        for robot_id in range(num_robots):
            pos = robot_start_positions[robot_id % len(robot_start_positions)]
            grid_world.robot_positions.append(pos)
            grid_world.grid[pos[0], pos[1]] = robot_id + 1
            grid_world.robot_paths[robot_id] = [pos]

        # Place goals in accessible positions
        goal_positions = [
            (1, grid_size//2), (grid_size-2, grid_size//2),
            (grid_size//2, 1), (grid_size//2, grid_size-2),
            (grid_size//4, grid_size//4)
        ]

        for task_id in range(num_tasks):
            if task_id < len(goal_positions):
                pos = goal_positions[task_id]
            else:
                # Find empty position
                while True:
                    x, y = random.randint(1, grid_size-2), random.randint(1, grid_size-2)
                    if grid_world.grid[x, y] == 0:
                        pos = (x, y)
                        break

            grid_world.goal_positions.append(pos)
            grid_world.grid[pos[0], pos[1]] = -(task_id + 1)

        return grid_world

    except Exception as e:
        print(f"Error creating grid from problem: {e}")
        # Fallback to simple grid
        return GridWorld(grid_size, 2, 5, num_obstacles=2)
