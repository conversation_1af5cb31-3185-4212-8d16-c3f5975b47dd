Experiment: Balanced Training
Success: False
Configuration: {'name': 'Balanced Training', 'lr': 0.0001, 'weight_decay': 5e-07, 'num_robots': 2, 'num_steps': 4000, 'batch_size': 24, 'checkpoint_interval': 100, 'checkpoint_dir': './experiments_enhanced/balanced', 'loss_type': 'multi_objective', 'target_loss': 0.005, 'dropout': 0.1, 'num_heads': 8, 'use_warmup': True}

STDOUT:


STDERR:
[Errno 2] No such file or directory: 'python'