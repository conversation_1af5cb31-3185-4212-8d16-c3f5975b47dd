#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
integrated_decentralized_testing.py

Integrated testing script that combines simulation capabilities with comprehensive testing.
Tests decentralized models on problem instances and provides makespan, workload balance, and time metrics.
"""

import os
import argparse
import time
import numpy as np
import pandas as pd
import torch
from typing import Dict, List, Tuple
import matplotlib.pyplot as plt

from utils import SchedulingEnv, build_hetgraph, hetgraph_node_helper
from hetnet import DecentralizedScheduleNet, MultiRobotDecentralizedSystem
from multi_objective_utils import calculate_workload_balance


class IntegratedDecentralizedTester:
    """
    Integrated tester that combines simulation and testing capabilities.
    Tests decentralized models on problem instances and provides comprehensive metrics.
    """
    
    def __init__(self, model_path: str, device: torch.device):
        self.device = device
        self.model_path = model_path
        
        # Load trained decentralized model
        self.decentralized_system, self.alpha, self.beta, self.step, self.num_robots = self._load_model(model_path)
        
        print(f"Loaded decentralized model: α={self.alpha:.3f}, β={self.beta:.3f}, step={self.step}")
        print(f"Number of robots: {self.num_robots}")
    
    def _load_model(self, model_path: str) -> Tuple:
        """Load the trained decentralized model."""
        checkpoint = torch.load(model_path, map_location=self.device)
        
        num_robots = checkpoint['num_robots']
        alpha = checkpoint.get('alpha', 0.5)
        beta = checkpoint.get('beta', 0.5)
        step = checkpoint.get('step', 0)
        
        # Network architecture (same as training)
        in_dim = {'task': 6, 'loc': 1, 'robot': 1, 'state': 4, 'value': 1}
        hid_dim = {'task': 64, 'loc': 64, 'robot': 64, 'state': 64, 'value': 64}
        out_dim = {'task': 32, 'loc': 32, 'robot': 32, 'state': 32, 'value': 1}
        cetypes = [
            ('task', 'temporal', 'task'),
            ('task', 'located_in', 'loc'), ('loc', 'near', 'loc'),
            ('task', 'assigned_to', 'robot'), ('robot', 'com', 'robot'),
            ('task', 'tin', 'state'), ('loc', 'lin', 'state'),
            ('robot', 'rin', 'state'), ('state', 'sin', 'state'),
            ('task', 'tto', 'value'), ('robot', 'rto', 'value'),
            ('state', 'sto', 'value'), ('value', 'vto', 'value'),
            ('task', 'take_time', 'robot'), ('robot', 'use_time', 'task')
        ]
        
        # Create decentralized system
        decentralized_system = MultiRobotDecentralizedSystem(
            in_dim, hid_dim, out_dim, cetypes, num_robots, 8
        ).to(self.device)
        
        # Load robot network states
        robot_networks = checkpoint['robot_networks']
        for robot_id in range(num_robots):
            robot_key = f'robot_{robot_id}'
            if robot_key in robot_networks:
                robot_net = decentralized_system.get_robot_network(robot_id)
                robot_net.load_state_dict(robot_networks[robot_key])
                robot_net.eval()
        
        return decentralized_system, alpha, beta, step, num_robots
    
    def solve_instance(self, prefix: str, num_tasks: int, communication_rounds: int = 2) -> Tuple[float, float, Dict, float, bool]:
        """
        Solve a single problem instance using the decentralized model.
        
        Returns:
            makespan, workload_balance, assignments, runtime, success
        """
        t0 = time.time()
        
        try:
            # Load environment
            env = SchedulingEnv(prefix)
            env.set_multi_objective_params(alpha=self.alpha, beta=self.beta)
            ok, mm = env.check_consistency_makespan(updateDG=False)
            if not ok:
                return float("nan"), float("nan"), {}, time.time() - t0, False
            env.min_makespan = mm
        except Exception as e:
            print(f"Error loading environment: {e}")
            return float("nan"), float("nan"), {}, time.time() - t0, False
        
        assignments = {r: [] for r in range(self.num_robots)}
        feasible_flag = True
        
        # Calculate map_width dynamically based on actual location data
        max_coord = max(np.max(env.loc[:, 0]), np.max(env.loc[:, 1])) if len(env.loc) > 0 else 6
        map_width = max(6, max_coord + 2)  # Add buffer for safety
        loc_dist_threshold = max(1, map_width // 4)
        
        step_count = 0
        decision_log = []
        
        while True:
            unsch_tasks = env.get_unscheduled_tasks()
            if len(unsch_tasks) == 0:
                break
            
            step_count += 1
            if step_count > num_tasks * 2:  # Safety check
                feasible_flag = False
                break
            
            # Decentralized decision making: each robot evaluates tasks independently
            robot_decisions = {}
            robot_confidences = {}
            
            # Phase 1: Each robot makes local decisions
            for robot_id in range(self.num_robots):
                try:
                    # Build graph from this robot's perspective
                    g = build_hetgraph(
                        env.halfDG,
                        num_tasks,
                        self.num_robots,
                        env.dur.astype(np.float32),
                        map_width,
                        np.array(env.loc, dtype=np.int64),
                        loc_dist_threshold,
                        env.partials,
                        np.array(unsch_tasks, dtype=np.int64),
                        robot_id,  # This robot's perspective
                        np.array(unsch_tasks, dtype=np.int64)
                    ).to(self.device)
                    
                    # Build features
                    feat_dict = hetgraph_node_helper(
                        env.halfDG.number_of_nodes(),
                        env.partialw,
                        env.partials,
                        env.loc,
                        env.dur,
                        map_width,
                        self.num_robots,
                        len(unsch_tasks)
                    )
                    
                    feat_tensors = {k: torch.tensor(v, device=self.device, dtype=torch.float32)
                                   for k, v in feat_dict.items()}
                    
                    # Forward pass with communication
                    with torch.no_grad():
                        outputs = self.decentralized_system.forward_with_communication(
                            robot_id, g, feat_tensors, communication_rounds=communication_rounds
                        )
                        q_values = outputs['value'].cpu().numpy().reshape(-1)
                        confidence = outputs['confidence'].cpu().numpy().reshape(-1)
                    
                    # Find best task for this robot
                    best_idx = np.argmax(q_values)
                    best_task = int(unsch_tasks[best_idx])
                    best_q = float(q_values[best_idx])
                    best_conf = float(confidence[best_idx])
                    
                    robot_decisions[robot_id] = {
                        'task': best_task,
                        'q_value': best_q,
                        'confidence': best_conf
                    }
                    robot_confidences[robot_id] = best_conf
                    
                except Exception as e:
                    print(f"Error in robot {robot_id} decision: {e}")
                    robot_decisions[robot_id] = {
                        'task': None,
                        'q_value': -float('inf'),
                        'confidence': 0.0
                    }
                    robot_confidences[robot_id] = 0.0
            
            # Phase 2: Conflict resolution - choose robot with highest confidence
            best_robot = None
            best_task = None
            best_confidence = -1.0
            
            for robot_id, decision in robot_decisions.items():
                if (decision['task'] is not None and
                    decision['confidence'] > best_confidence):
                    best_confidence = decision['confidence']
                    best_robot = robot_id
                    best_task = decision['task']
            
            # Fallback: if no robot has confidence, use the one with highest Q-value
            if best_robot is None:
                best_q = -float('inf')
                for robot_id, decision in robot_decisions.items():
                    if (decision['task'] is not None and
                        decision['q_value'] > best_q):
                        best_q = decision['q_value']
                        best_robot = robot_id
                        best_task = decision['task']
            
            if best_robot is None or best_task is None:
                feasible_flag = False
                break
            
            # Log decision for analysis
            decision_log.append({
                'step': step_count,
                'robot': best_robot,
                'task': best_task,
                'confidence': best_confidence,
                'all_decisions': robot_decisions.copy()
            })
            
            # Execute best action
            success, _, done_flag = env.insert_robot(best_task, best_robot)
            if not success:
                feasible_flag = False
                break
            
            assignments[best_robot].append(best_task)
            
            if done_flag:
                break
        
        runtime = time.time() - t0
        
        if not feasible_flag:
            return float("nan"), float("nan"), assignments, runtime, False
        
        # Calculate final metrics
        try:
            ok_final, final_makespan = env.check_consistency_makespan(updateDG=False)
            if not ok_final:
                return float("nan"), float("nan"), assignments, runtime, False
            
            workload_balance = calculate_workload_balance(assignments, self.num_robots)
            
            return final_makespan, workload_balance, assignments, runtime, True
            
        except Exception as e:
            print(f"Error calculating final metrics: {e}")
            return float("nan"), float("nan"), assignments, runtime, False
    
    def test_on_instances(self, test_data_path: str, num_tasks: int, max_instances: int = 100, 
                         communication_rounds: int = 2, save_results: bool = True) -> Dict:
        """
        Test the decentralized model on multiple problem instances.
        
        Args:
            test_data_path: Path to test data directory (constraints folder)
            num_tasks: Number of tasks per instance
            max_instances: Maximum number of instances to test
            communication_rounds: Number of communication rounds
            save_results: Whether to save results to CSV
            
        Returns:
            Dictionary with comprehensive test results
        """
        print(f"\n=== Testing Decentralized Model ===")
        print(f"Test data: {test_data_path}")
        print(f"Problem size: {num_tasks} tasks, {self.num_robots} robots")
        print(f"Max instances: {max_instances}")
        print(f"Communication rounds: {communication_rounds}")
        
        # Prepare results storage
        results = {
            'instance_ids': [],
            'makespans': [],
            'workload_balances': [],
            'runtimes': [],
            'feasible': [],
            'assignments': [],
            'success_rate': 0.0,
            'avg_makespan': 0.0,
            'avg_workload_balance': 0.0,
            'avg_runtime': 0.0
        }
        
        successful_instances = 0
        total_instances = 0
        
        print(f"\nTesting instances...")
        for inst_id in range(1, max_instances + 1):
            prefix = os.path.join(test_data_path, f"{inst_id:05d}")
            
            # Check if instance exists
            if not os.path.isfile(f"{prefix}_dur.txt"):
                continue
            
            total_instances += 1
            print(f"[Instance {inst_id:05d}] Testing...", end=" ")
            
            # Test the instance
            makespan, balance, assigns, runtime, success = self.solve_instance(
                prefix, num_tasks, communication_rounds
            )
            
            # Store results
            results['instance_ids'].append(f"{inst_id:05d}")
            results['makespans'].append(makespan)
            results['workload_balances'].append(balance)
            results['runtimes'].append(runtime)
            results['feasible'].append(1 if success else 0)
            results['assignments'].append(assigns)
            
            if success:
                successful_instances += 1
                print(f"✓ Makespan: {makespan:.2f}, Balance: {balance:.3f}, Time: {runtime:.3f}s")
            else:
                print(f"✗ Failed")
            
            # Progress update
            if total_instances % 10 == 0:
                success_rate = successful_instances / total_instances * 100
                print(f"Progress: {total_instances} instances, {success_rate:.1f}% success rate")
        
        # Calculate summary statistics
        if successful_instances > 0:
            valid_makespans = [m for m, f in zip(results['makespans'], results['feasible']) if f == 1]
            valid_balances = [b for b, f in zip(results['workload_balances'], results['feasible']) if f == 1]
            valid_runtimes = [r for r, f in zip(results['runtimes'], results['feasible']) if f == 1]
            
            results['success_rate'] = successful_instances / total_instances
            results['avg_makespan'] = np.mean(valid_makespans) if valid_makespans else float('nan')
            results['avg_workload_balance'] = np.mean(valid_balances) if valid_balances else float('nan')
            results['avg_runtime'] = np.mean(valid_runtimes) if valid_runtimes else float('nan')
            results['std_makespan'] = np.std(valid_makespans) if valid_makespans else float('nan')
            results['std_workload_balance'] = np.std(valid_balances) if valid_balances else float('nan')
            results['std_runtime'] = np.std(valid_runtimes) if valid_runtimes else float('nan')
        
        # Save results to CSV if requested
        if save_results:
            self._save_results_to_csv(results, test_data_path)
        
        return results
    
    def _save_results_to_csv(self, results: Dict, test_data_path: str):
        """Save test results to CSV files."""
        # Create output directory
        output_dir = f"./csv_outputs_decentralized_a{self.alpha:.1f}_b{self.beta:.1f}"
        os.makedirs(output_dir, exist_ok=True)
        
        # Prepare makespan results
        makespan_df = pd.DataFrame({
            'instance_id': results['instance_ids'],
            'makespan': results['makespans'],
            'feasible': results['feasible'],
            'runtime': results['runtimes']
        })
        
        # Prepare workload balance results
        balance_df = pd.DataFrame({
            'instance_id': results['instance_ids'],
            'workload_balance': results['workload_balances'],
            'feasible': results['feasible'],
            'runtime': results['runtimes']
        })
        
        # Save CSV files
        makespan_file = os.path.join(output_dir, f"decentralized_a{self.alpha:.1f}_b{self.beta:.1f}_makespan.csv")
        balance_file = os.path.join(output_dir, f"decentralized_a{self.alpha:.1f}_b{self.beta:.1f}_workload_balance.csv")
        
        makespan_df.to_csv(makespan_file, index=False)
        balance_df.to_csv(balance_file, index=False)
        
        print(f"\n📊 Results saved:")
        print(f"  Makespan: {makespan_file}")
        print(f"  Workload Balance: {balance_file}")
    
    def print_summary(self, results: Dict):
        """Print comprehensive test summary."""
        print("\n" + "="*60)
        print("DECENTRALIZED MODEL TEST RESULTS")
        print("="*60)
        print(f"Model: {os.path.basename(self.model_path)}")
        print(f"Parameters: α={self.alpha:.3f}, β={self.beta:.3f}, step={self.step}")
        print(f"Robots: {self.num_robots}")
        print("-"*60)
        print(f"Total instances tested: {len(results['instance_ids'])}")
        print(f"Successful instances: {sum(results['feasible'])}")
        print(f"Success rate: {results['success_rate']*100:.1f}%")
        print("-"*60)
        
        if results['success_rate'] > 0:
            print(f"Average makespan: {results['avg_makespan']:.3f} ± {results['std_makespan']:.3f}")
            print(f"Average workload balance: {results['avg_workload_balance']:.3f} ± {results['std_workload_balance']:.3f}")
            print(f"Average runtime: {results['avg_runtime']:.3f} ± {results['std_runtime']:.3f} seconds")
        else:
            print("No successful instances to report statistics.")
        
        print("="*60)


def main():
    parser = argparse.ArgumentParser(description="Integrated Decentralized Model Testing")
    parser.add_argument("--model-path", required=True, help="Path to trained decentralized model")
    parser.add_argument("--test-data", required=True, help="Path to test data directory (constraints folder)")
    parser.add_argument("--num-tasks", type=int, default=5, help="Number of tasks per instance")
    parser.add_argument("--max-instances", type=int, default=100, help="Maximum number of instances to test")
    parser.add_argument("--communication-rounds", type=int, default=2, help="Number of communication rounds")
    parser.add_argument("--device", default="cpu", help="Device (cpu or cuda)")
    parser.add_argument("--no-save", action="store_true", help="Don't save results to CSV")
    
    args = parser.parse_args()
    
    device = torch.device(args.device)
    
    print("Integrated Decentralized Model Testing")
    print("="*50)
    print(f"Model: {args.model_path}")
    print(f"Test data: {args.test_data}")
    print(f"Problem size: {args.num_tasks} tasks")
    print(f"Max instances: {args.max_instances}")
    print(f"Communication rounds: {args.communication_rounds}")
    
    # Create tester and run tests
    tester = IntegratedDecentralizedTester(args.model_path, device)
    
    results = tester.test_on_instances(
        args.test_data,
        args.num_tasks,
        args.max_instances,
        args.communication_rounds,
        save_results=not args.no_save
    )
    
    # Print summary
    tester.print_summary(results)


if __name__ == "__main__":
    main()
