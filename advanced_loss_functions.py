#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
advanced_loss_functions.py

Advanced loss functions for improved training convergence and stability.
Includes focal loss, Huber loss, progressive loss scaling, and multi-objective losses.
"""

import torch
import torch.nn as nn
import torch.nn.functional as F
import numpy as np


class FocalLoss(nn.Module):
    """
    Focal Loss for addressing class imbalance and hard examples.
    Helps the model focus on difficult examples and achieve better convergence.
    """
    def __init__(self, alpha=1.0, gamma=2.0, reduction='mean'):
        super(Focal<PERSON>oss, self).__init__()
        self.alpha = alpha
        self.gamma = gamma
        self.reduction = reduction

    def forward(self, inputs, targets):
        # Calculate standard MSE loss
        mse_loss = F.mse_loss(inputs, targets, reduction='none')
        
        # Calculate focal weight: (1 - exp(-mse_loss))^gamma
        # This gives higher weight to larger errors (harder examples)
        focal_weight = (1 - torch.exp(-mse_loss)) ** self.gamma
        
        # Apply focal weighting
        focal_loss = self.alpha * focal_weight * mse_loss
        
        if self.reduction == 'mean':
            return focal_loss.mean()
        elif self.reduction == 'sum':
            return focal_loss.sum()
        else:
            return focal_loss


class AdaptiveHuberLoss(nn.Module):
    """
    Adaptive Huber Loss that automatically adjusts the delta parameter
    based on the current loss magnitude for better stability.
    """
    def __init__(self, delta_init=1.0, adaptation_rate=0.01, min_delta=0.1, max_delta=10.0):
        super(AdaptiveHuberLoss, self).__init__()
        self.delta = nn.Parameter(torch.tensor(delta_init))
        self.adaptation_rate = adaptation_rate
        self.min_delta = min_delta
        self.max_delta = max_delta

    def forward(self, inputs, targets):
        diff = inputs - targets
        abs_diff = torch.abs(diff)
        
        # Huber loss calculation
        quadratic = torch.min(abs_diff, self.delta)
        linear = abs_diff - quadratic
        loss = 0.5 * quadratic ** 2 + self.delta * linear
        
        # Adapt delta based on current loss magnitude
        with torch.no_grad():
            avg_loss = loss.mean()
            if avg_loss > self.delta:
                self.delta.data = torch.clamp(
                    self.delta * (1 + self.adaptation_rate), 
                    self.min_delta, self.max_delta
                )
            else:
                self.delta.data = torch.clamp(
                    self.delta * (1 - self.adaptation_rate), 
                    self.min_delta, self.max_delta
                )
        
        return loss.mean()


class ProgressiveLossScaler:
    """
    Progressive loss scaling that adjusts loss magnitude during training
    to achieve gradual convergence below target thresholds.
    """
    def __init__(self, initial_scale=1.0, target_loss=0.01, decay_rate=0.995, min_scale=0.1):
        self.scale = initial_scale
        self.target_loss = target_loss
        self.decay_rate = decay_rate
        self.min_scale = min_scale
        self.step_count = 0
        
    def update_scale(self, current_loss):
        """Update the loss scale based on current loss value"""
        self.step_count += 1
        
        # If loss is above target, maintain or increase scale
        if current_loss > self.target_loss:
            # Gradually increase scale if loss is too high
            if current_loss > self.target_loss * 10:
                self.scale = min(self.scale * 1.01, 2.0)
        else:
            # Gradually decrease scale as we approach target
            self.scale = max(self.scale * self.decay_rate, self.min_scale)
            
        return self.scale
    
    def scale_loss(self, loss):
        """Apply current scale to the loss"""
        return loss * self.scale


class MultiObjectiveLoss(nn.Module):
    """
    Multi-objective loss function that combines multiple loss components
    with adaptive weighting for better convergence.
    """
    def __init__(self, loss_weights=None, adaptive_weighting=True):
        super(MultiObjectiveLoss, self).__init__()
        
        # Default loss weights
        if loss_weights is None:
            loss_weights = {
                'mse': 0.4,
                'focal': 0.3,
                'huber': 0.2,
                'regularization': 0.1
            }
        
        self.loss_weights = loss_weights
        self.adaptive_weighting = adaptive_weighting
        
        # Initialize loss functions
        self.mse_loss = nn.MSELoss()
        self.focal_loss = FocalLoss(alpha=0.5, gamma=1.5)
        self.huber_loss = AdaptiveHuberLoss(delta_init=0.5)
        
        # Track loss history for adaptive weighting
        self.loss_history = {key: [] for key in loss_weights.keys()}
        
    def forward(self, inputs, targets, model_params=None):
        # Calculate individual losses
        mse = self.mse_loss(inputs, targets)
        focal = self.focal_loss(inputs, targets)
        huber = self.huber_loss(inputs, targets)
        
        # Regularization loss (L2 penalty on model parameters)
        reg_loss = torch.tensor(0.0, device=inputs.device)
        if model_params is not None:
            for param in model_params:
                reg_loss += torch.norm(param, p=2)
            reg_loss = reg_loss * 1e-5  # Small regularization coefficient
        
        # Store loss values
        losses = {
            'mse': mse,
            'focal': focal,
            'huber': huber,
            'regularization': reg_loss
        }
        
        # Update loss history
        for key, value in losses.items():
            self.loss_history[key].append(value.item())
            # Keep only recent history
            if len(self.loss_history[key]) > 100:
                self.loss_history[key] = self.loss_history[key][-100:]
        
        # Adaptive weighting based on loss trends
        if self.adaptive_weighting and len(self.loss_history['mse']) > 10:
            weights = self._compute_adaptive_weights()
        else:
            weights = self.loss_weights
        
        # Combine losses
        total_loss = sum(weights[key] * losses[key] for key in weights.keys())
        
        return total_loss, losses
    
    def _compute_adaptive_weights(self):
        """Compute adaptive weights based on loss trends"""
        weights = self.loss_weights.copy()
        
        # Calculate recent trends for each loss
        for key in weights.keys():
            if len(self.loss_history[key]) >= 10:
                recent_losses = self.loss_history[key][-10:]
                trend = np.polyfit(range(len(recent_losses)), recent_losses, 1)[0]
                
                # If loss is increasing, increase its weight
                if trend > 0:
                    weights[key] *= 1.1
                # If loss is decreasing well, slightly reduce its weight
                elif trend < -0.001:
                    weights[key] *= 0.95
        
        # Normalize weights
        total_weight = sum(weights.values())
        weights = {key: value / total_weight for key, value in weights.items()}
        
        return weights


class WarmupCosineScheduler:
    """
    Learning rate scheduler with warmup and cosine annealing for better training dynamics.
    """
    def __init__(self, optimizer, warmup_steps=1000, total_steps=10000, 
                 min_lr_ratio=0.01, warmup_lr_ratio=0.1):
        self.optimizer = optimizer
        self.warmup_steps = warmup_steps
        self.total_steps = total_steps
        self.min_lr_ratio = min_lr_ratio
        self.warmup_lr_ratio = warmup_lr_ratio
        
        # Store initial learning rates
        self.base_lrs = [group['lr'] for group in optimizer.param_groups]
        self.current_step = 0
        
    def step(self):
        """Update learning rate for current step"""
        self.current_step += 1
        
        for i, param_group in enumerate(self.optimizer.param_groups):
            base_lr = self.base_lrs[i]
            
            if self.current_step <= self.warmup_steps:
                # Warmup phase: linear increase
                lr = base_lr * self.warmup_lr_ratio + \
                     (base_lr - base_lr * self.warmup_lr_ratio) * \
                     (self.current_step / self.warmup_steps)
            else:
                # Cosine annealing phase
                progress = (self.current_step - self.warmup_steps) / \
                          (self.total_steps - self.warmup_steps)
                progress = min(progress, 1.0)
                
                lr = base_lr * self.min_lr_ratio + \
                     (base_lr - base_lr * self.min_lr_ratio) * \
                     0.5 * (1 + np.cos(np.pi * progress))
            
            param_group['lr'] = lr
        
        return self.optimizer.param_groups[0]['lr']


def create_advanced_loss_function(loss_type='multi_objective', **kwargs):
    """
    Factory function to create advanced loss functions.
    
    Args:
        loss_type: Type of loss function ('focal', 'huber', 'multi_objective')
        **kwargs: Additional arguments for the loss function
    
    Returns:
        Loss function instance
    """
    if loss_type == 'focal':
        return FocalLoss(**kwargs)
    elif loss_type == 'huber':
        return AdaptiveHuberLoss(**kwargs)
    elif loss_type == 'multi_objective':
        return MultiObjectiveLoss(**kwargs)
    else:
        raise ValueError(f"Unknown loss type: {loss_type}")
