#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
simulation_integrated_testing.py

Integrated testing script that runs MiniGrid simulation while solving each problem instance.
Shows robots moving in the grid environment and provides makespan, workload balance, and time metrics.
"""

import os
import argparse
import time
import numpy as np
import torch
import matplotlib.pyplot as plt
from typing import Dict, List, Tuple

from utils import SchedulingEnv, build_hetgraph, hetgraph_node_helper
from hetnet import DecentralizedScheduleNet, MultiRobotDecentralizedSystem
from multi_objective_utils import calculate_workload_balance
from minigrid_environment import GridWorld, GridWorldVisualizer, create_grid_from_scheduling_problem


class SimulationIntegratedTester:
    """
    Integrated tester that runs MiniGrid simulation while solving problem instances.
    """
    
    def __init__(self, model_path: str, device: torch.device, grid_size: int = 10):
        self.device = device
        self.model_path = model_path
        self.grid_size = grid_size
        
        # Load trained decentralized model
        self.decentralized_system, self.alpha, self.beta, self.step, self.num_robots = self._load_model(model_path)
        
        print(f"🤖 Loaded decentralized model: α={self.alpha:.3f}, β={self.beta:.3f}, step={self.step}")
        print(f"🔢 Number of robots: {self.num_robots}")
        print(f"🎮 Grid size: {grid_size}×{grid_size}")
    
    def _load_model(self, model_path: str) -> Tuple:
        """Load the trained decentralized model."""
        checkpoint = torch.load(model_path, map_location=self.device)
        
        num_robots = checkpoint['num_robots']
        alpha = checkpoint.get('alpha', 0.5)
        beta = checkpoint.get('beta', 0.5)
        step = checkpoint.get('step', 0)
        
        # Network architecture
        in_dim = {'task': 6, 'loc': 1, 'robot': 1, 'state': 4, 'value': 1}
        hid_dim = {'task': 64, 'loc': 64, 'robot': 64, 'state': 64, 'value': 64}
        out_dim = {'task': 32, 'loc': 32, 'robot': 32, 'state': 32, 'value': 1}
        cetypes = [
            ('task', 'temporal', 'task'),
            ('task', 'located_in', 'loc'), ('loc', 'near', 'loc'),
            ('task', 'assigned_to', 'robot'), ('robot', 'com', 'robot'),
            ('task', 'tin', 'state'), ('loc', 'lin', 'state'),
            ('robot', 'rin', 'state'), ('state', 'sin', 'state'),
            ('task', 'tto', 'value'), ('robot', 'rto', 'value'),
            ('state', 'sto', 'value'), ('value', 'vto', 'value'),
            ('task', 'take_time', 'robot'), ('robot', 'use_time', 'task')
        ]
        
        # Create decentralized system
        decentralized_system = MultiRobotDecentralizedSystem(
            in_dim, hid_dim, out_dim, cetypes, num_robots, 8
        ).to(self.device)
        
        # Load robot network states
        robot_networks = checkpoint['robot_networks']
        for robot_id in range(num_robots):
            robot_key = f'robot_{robot_id}'
            if robot_key in robot_networks:
                robot_net = decentralized_system.get_robot_network(robot_id)
                robot_net.load_state_dict(robot_networks[robot_key])
                robot_net.eval()
        
        return decentralized_system, alpha, beta, step, num_robots
    
    def solve_instance_with_simulation(self, prefix: str, num_tasks: int, 
                                     show_simulation: bool = True, 
                                     save_frames: bool = False,
                                     communication_rounds: int = 2) -> Tuple[float, float, Dict, float, bool]:
        """
        Solve a problem instance with MiniGrid simulation.
        
        Returns:
            makespan, workload_balance, assignments, runtime, success
        """
        t0 = time.time()
        
        print(f"\n🎮 Starting simulation for instance: {os.path.basename(prefix)}")
        
        try:
            # Create grid world from problem instance
            grid_world = create_grid_from_scheduling_problem(prefix, self.grid_size)
            
            # Create scheduling environment for neural network
            env = SchedulingEnv(prefix)
            env.set_multi_objective_params(alpha=self.alpha, beta=self.beta)
            ok, mm = env.check_consistency_makespan(updateDG=False)
            if not ok:
                print("❌ Problem instance is infeasible")
                return float("nan"), float("nan"), {}, time.time() - t0, False
            env.min_makespan = mm
            
        except Exception as e:
            print(f"❌ Error loading environment: {e}")
            return float("nan"), float("nan"), {}, time.time() - t0, False
        
        # Initialize simulation
        if show_simulation:
            plt.ion()
            visualizer = GridWorldVisualizer(grid_world)
            visualizer.render(title=f"Instance: {os.path.basename(prefix)}")
            plt.show()
            time.sleep(1)
        
        assignments = {r: [] for r in range(self.num_robots)}
        feasible_flag = True
        
        # Calculate map_width dynamically
        max_coord = max(np.max(env.loc[:, 0]), np.max(env.loc[:, 1])) if len(env.loc) > 0 else 6
        map_width = max(6, max_coord + 2)
        loc_dist_threshold = max(1, map_width // 4)
        
        step_count = 0
        simulation_step = 0
        
        print(f"🚀 Starting decentralized decision making...")
        
        while True:
            unsch_tasks = env.get_unscheduled_tasks()
            if len(unsch_tasks) == 0:
                print("✅ All tasks completed!")
                break
            
            step_count += 1
            if step_count > num_tasks * 3:  # Safety check
                print("⚠️ Maximum steps reached")
                feasible_flag = False
                break
            
            print(f"\n📋 Step {step_count}: {len(unsch_tasks)} tasks remaining")
            
            # Decentralized decision making
            robot_decisions = {}
            
            # Each robot makes a decision
            for robot_id in range(self.num_robots):
                try:
                    # Build graph from this robot's perspective
                    g = build_hetgraph(
                        env.halfDG,
                        num_tasks,
                        self.num_robots,
                        env.dur.astype(np.float32),
                        map_width,
                        np.array(env.loc, dtype=np.int64),
                        loc_dist_threshold,
                        env.partials,
                        np.array(unsch_tasks, dtype=np.int64),
                        robot_id,
                        np.array(unsch_tasks, dtype=np.int64)
                    ).to(self.device)
                    
                    # Build features
                    feat_dict = hetgraph_node_helper(
                        env.halfDG.number_of_nodes(),
                        env.partialw,
                        env.partials,
                        env.loc,
                        env.dur,
                        map_width,
                        self.num_robots,
                        len(unsch_tasks)
                    )
                    
                    feat_tensors = {k: torch.tensor(v, device=self.device, dtype=torch.float32)
                                   for k, v in feat_dict.items()}
                    
                    # Forward pass with communication
                    with torch.no_grad():
                        outputs = self.decentralized_system.forward_with_communication(
                            robot_id, g, feat_tensors, communication_rounds=communication_rounds
                        )
                        q_values = outputs['value'].cpu().numpy().reshape(-1)
                        confidence = outputs['confidence'].cpu().numpy().reshape(-1)
                    
                    # Find best task for this robot
                    best_idx = np.argmax(q_values)
                    best_task = int(unsch_tasks[best_idx])
                    best_q = float(q_values[best_idx])
                    best_conf = float(confidence[best_idx])
                    
                    robot_decisions[robot_id] = {
                        'task': best_task,
                        'q_value': best_q,
                        'confidence': best_conf
                    }
                    
                    print(f"  🤖 Robot {robot_id}: Task {best_task} (conf: {best_conf:.3f}, q: {best_q:.3f})")
                    
                except Exception as e:
                    print(f"  ❌ Robot {robot_id} decision error: {e}")
                    robot_decisions[robot_id] = {
                        'task': None,
                        'q_value': -float('inf'),
                        'confidence': 0.0
                    }
            
            # Conflict resolution - choose robot with highest confidence
            best_robot = None
            best_task = None
            best_confidence = -1.0
            
            for robot_id, decision in robot_decisions.items():
                if (decision['task'] is not None and
                    decision['confidence'] > best_confidence):
                    best_confidence = decision['confidence']
                    best_robot = robot_id
                    best_task = decision['task']
            
            # Fallback: use highest Q-value
            if best_robot is None:
                best_q = -float('inf')
                for robot_id, decision in robot_decisions.items():
                    if (decision['task'] is not None and
                        decision['q_value'] > best_q):
                        best_q = decision['q_value']
                        best_robot = robot_id
                        best_task = decision['task']
            
            if best_robot is None or best_task is None:
                print("❌ No valid robot decision found")
                feasible_flag = False
                break
            
            print(f"  🎯 Selected: Robot {best_robot} → Task {best_task} (conf: {best_confidence:.3f})")
            
            # Execute best action in scheduling environment
            success, _, done_flag = env.insert_robot(best_task, best_robot)
            if not success:
                print("❌ Failed to execute action in scheduling environment")
                feasible_flag = False
                break
            
            assignments[best_robot].append(best_task)
            
            # Simulate robot movement in grid world
            if show_simulation:
                # Find corresponding goal in grid world
                task_idx = best_task - 1  # Convert to 0-based
                if task_idx < len(grid_world.goal_positions):
                    goal_pos = grid_world.goal_positions[task_idx]
                    robot_pos = grid_world.robot_positions[best_robot]
                    
                    print(f"  🎮 Simulating Robot {best_robot} moving to goal {task_idx} at {goal_pos}")
                    
                    # Simple path planning and execution
                    path_steps = self._plan_and_execute_path(grid_world, best_robot, goal_pos, visualizer, save_frames, simulation_step)
                    simulation_step += path_steps
                    
                    # Mark goal as visited
                    if task_idx not in grid_world.visited_goals:
                        grid_world.visited_goals.add(task_idx)
                        print(f"  ✅ Goal {task_idx} completed by Robot {best_robot}")
            
            if done_flag:
                print("🏁 All tasks scheduled!")
                break
        
        runtime = time.time() - t0
        
        if show_simulation:
            # Final visualization
            visualizer.render(title=f"Final State - Instance: {os.path.basename(prefix)}")
            time.sleep(2)
            plt.ioff()
        
        if not feasible_flag:
            return float("nan"), float("nan"), assignments, runtime, False
        
        # Calculate final metrics
        try:
            ok_final, final_makespan = env.check_consistency_makespan(updateDG=False)
            if not ok_final:
                return float("nan"), float("nan"), assignments, runtime, False
            
            workload_balance = calculate_workload_balance(assignments, self.num_robots)
            
            print(f"📊 Results: Makespan={final_makespan:.2f}, Balance={workload_balance:.3f}, Time={runtime:.2f}s")
            
            return final_makespan, workload_balance, assignments, runtime, True
            
        except Exception as e:
            print(f"❌ Error calculating final metrics: {e}")
            return float("nan"), float("nan"), assignments, runtime, False
    
    def _plan_and_execute_path(self, grid_world: GridWorld, robot_id: int, goal_pos: Tuple[int, int], 
                              visualizer: GridWorldVisualizer, save_frames: bool, simulation_step: int) -> int:
        """Plan and execute path for robot to goal with visualization."""
        robot_pos = grid_world.robot_positions[robot_id]
        steps_taken = 0
        
        while robot_pos != goal_pos and steps_taken < 50:  # Safety limit
            rx, ry = robot_pos
            gx, gy = goal_pos
            
            # Simple greedy movement
            action = 0  # Stay by default
            if gx < rx:  # Move up
                action = 1
            elif gx > rx:  # Move down
                action = 3
            elif gy < ry:  # Move left
                action = 4
            elif gy > ry:  # Move right
                action = 2
            
            # Execute movement
            success = grid_world.move_robot(robot_id, action)
            if success:
                robot_pos = grid_world.robot_positions[robot_id]
                steps_taken += 1
                
                # Update visualization
                visualizer.render(title=f"Robot {robot_id} moving to Goal (Step {simulation_step + steps_taken})")
                if save_frames:
                    visualizer.save_frame(f"frame_{simulation_step + steps_taken:04d}.png")
                time.sleep(0.3)  # Pause for visualization
            else:
                break
        
        return steps_taken
    
    def test_instances_with_simulation(self, test_data_path: str, num_tasks: int, 
                                     max_instances: int = 10, show_simulation: bool = True,
                                     save_frames: bool = False, communication_rounds: int = 2) -> Dict:
        """Test multiple instances with simulation."""
        
        print("🎮 SIMULATION INTEGRATED TESTING")
        print("="*60)
        print(f"📁 Test data: {test_data_path}")
        print(f"📊 Problem size: {num_tasks} tasks, {self.num_robots} robots")
        print(f"🔢 Max instances: {max_instances}")
        print(f"🎮 Show simulation: {show_simulation}")
        print(f"📸 Save frames: {save_frames}")
        print("="*60)
        
        results = {
            'successful': 0,
            'total': 0,
            'makespans': [],
            'workload_balances': [],
            'runtimes': [],
            'instance_ids': []
        }
        
        for inst_id in range(1, max_instances + 1):
            prefix = os.path.join(test_data_path, f"{inst_id:05d}")
            
            # Check if instance exists
            if not os.path.isfile(f"{prefix}_dur.txt"):
                continue
            
            results['total'] += 1
            print(f"\n🎯 [Instance {inst_id:05d}] Starting simulation...")
            
            # Test the instance with simulation
            makespan, balance, assigns, runtime, success = self.solve_instance_with_simulation(
                prefix, num_tasks, show_simulation, save_frames, communication_rounds
            )
            
            if success:
                results['successful'] += 1
                results['makespans'].append(makespan)
                results['workload_balances'].append(balance)
                results['runtimes'].append(runtime)
                results['instance_ids'].append(f"{inst_id:05d}")
                print(f"✅ SUCCESS: Makespan: {makespan:.2f}, Balance: {balance:.3f}, Time: {runtime:.2f}s")
            else:
                print(f"❌ FAILED")
            
            # Progress update
            if results['total'] % 5 == 0:
                success_rate = results['successful'] / results['total'] * 100
                print(f"\n📈 Progress: {results['total']} instances, {success_rate:.1f}% success rate")
            
            # Pause between instances
            if show_simulation and results['total'] < max_instances:
                print("⏸️ Pausing 2 seconds before next instance...")
                time.sleep(2)
        
        # Print final summary
        self._print_summary(results)
        return results
    
    def _print_summary(self, results: Dict):
        """Print comprehensive test summary."""
        print("\n" + "="*60)
        print("🏆 SIMULATION TEST RESULTS SUMMARY")
        print("="*60)
        print(f"📁 Model: {os.path.basename(self.model_path)}")
        print(f"⚙️ Parameters: α={self.alpha:.3f}, β={self.beta:.3f}, step={self.step}")
        print(f"🤖 Robots: {self.num_robots}")
        print(f"🎮 Grid size: {self.grid_size}×{self.grid_size}")
        print("-"*60)
        print(f"📊 Total instances tested: {results['total']}")
        print(f"✅ Successful instances: {results['successful']}")
        print(f"📈 Success rate: {results['successful']/results['total']*100:.1f}%")
        print("-"*60)
        
        if results['successful'] > 0:
            print(f"📏 Average makespan: {np.mean(results['makespans']):.3f} ± {np.std(results['makespans']):.3f}")
            print(f"⚖️ Average workload balance: {np.mean(results['workload_balances']):.3f} ± {np.std(results['workload_balances']):.3f}")
            print(f"⏱️ Average runtime: {np.mean(results['runtimes']):.3f} ± {np.std(results['runtimes']):.3f} seconds")
            print(f"🏅 Best makespan: {np.min(results['makespans']):.3f}")
            print(f"🏅 Best workload balance: {np.min(results['workload_balances']):.3f}")
        else:
            print("❌ No successful instances to report statistics.")
        
        print("="*60)


def main():
    parser = argparse.ArgumentParser(description="Simulation Integrated Decentralized Testing")
    parser.add_argument("--model-path", required=True, help="Path to trained decentralized model")
    parser.add_argument("--test-data", required=True, help="Path to test data directory (constraints folder)")
    parser.add_argument("--num-tasks", type=int, default=5, help="Number of tasks per instance")
    parser.add_argument("--max-instances", type=int, default=10, help="Maximum number of instances to test")
    parser.add_argument("--grid-size", type=int, default=8, help="Grid size for simulation")
    parser.add_argument("--communication-rounds", type=int, default=2, help="Number of communication rounds")
    parser.add_argument("--device", default="cpu", help="Device (cpu or cuda)")
    parser.add_argument("--no-simulation", action="store_true", help="Disable visual simulation")
    parser.add_argument("--save-frames", action="store_true", help="Save simulation frames as images")
    
    args = parser.parse_args()
    
    device = torch.device(args.device)
    
    # Create tester and run simulation tests
    tester = SimulationIntegratedTester(args.model_path, device, args.grid_size)
    
    results = tester.test_instances_with_simulation(
        args.test_data,
        args.num_tasks,
        args.max_instances,
        show_simulation=not args.no_simulation,
        save_frames=args.save_frames,
        communication_rounds=args.communication_rounds
    )


if __name__ == "__main__":
    main()
