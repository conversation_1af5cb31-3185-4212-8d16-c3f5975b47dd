#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
plot_enhanced_training.py

Visualization script for enhanced training results with detailed analysis
of loss convergence, component losses, and training dynamics.
"""

import os
import torch
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
from scipy import stats
import argparse


def load_training_metrics(checkpoint_path):
    """Load training metrics from checkpoint"""
    checkpoint = torch.load(checkpoint_path, map_location='cpu')
    return checkpoint.get('training_metrics', {}), checkpoint.get('args', {})


def plot_comprehensive_training_analysis(metrics, args, save_path='enhanced_training_analysis.png'):
    """Create comprehensive training analysis plots"""

    # Set up the plotting style
    plt.style.use('seaborn-v0_8')
    sns.set_palette("husl")

    fig = plt.figure(figsize=(20, 15))

    # 1. Loss convergence with target line
    plt.subplot(3, 4, 1)
    loss_history = metrics.get('loss_history', [])
    if loss_history:
        plt.plot(loss_history, linewidth=2, alpha=0.8, label='Training Loss')
        plt.axhline(y=getattr(args, 'target_loss', 0.01), color='red',
                   linestyle='--', linewidth=2, label=f'Target Loss ({getattr(args, "target_loss", 0.01)})')
        plt.yscale('log')
        plt.xlabel('Training Step')
        plt.ylabel('Loss (log scale)')
        plt.title('Loss Convergence')
        plt.legend()
        plt.grid(True, alpha=0.3)

        # Add trend line
        if len(loss_history) > 10:
            x = np.arange(len(loss_history))
            z = np.polyfit(x[-len(loss_history)//2:], loss_history[-len(loss_history)//2:], 1)
            p = np.poly1d(z)
            plt.plot(x[-len(loss_history)//2:], p(x[-len(loss_history)//2:]),
                    "r--", alpha=0.5, label='Trend')

    # 2. Component losses (if available)
    plt.subplot(3, 4, 2)
    component_losses = metrics.get('component_losses', {})
    if component_losses:
        for component, values in component_losses.items():
            if values:
                plt.plot(values, label=component.capitalize(), alpha=0.8)
        plt.yscale('log')
        plt.xlabel('Training Step')
        plt.ylabel('Component Loss (log scale)')
        plt.title('Component Loss Breakdown')
        plt.legend()
        plt.grid(True, alpha=0.3)

    # 3. Q-value predictions vs targets
    plt.subplot(3, 4, 3)
    q_pred = metrics.get('q_pred_history', [])
    q_target = metrics.get('q_target_history', [])
    if q_pred and q_target:
        plt.plot(q_pred, label='Q Predictions', alpha=0.8)
        plt.plot(q_target, label='Q Targets', alpha=0.8)
        plt.xlabel('Training Step')
        plt.ylabel('Q-Value')
        plt.title('Q-Values: Predictions vs Targets')
        plt.legend()
        plt.grid(True, alpha=0.3)

    # 4. Prediction error over time
    plt.subplot(3, 4, 4)
    if q_pred and q_target:
        pred_errors = [abs(p - t) for p, t in zip(q_pred, q_target)]
        plt.plot(pred_errors, color='orange', alpha=0.8)
        plt.yscale('log')
        plt.xlabel('Training Step')
        plt.ylabel('|Q_pred - Q_target| (log scale)')
        plt.title('Prediction Error')
        plt.grid(True, alpha=0.3)

    # 5. Learning rate schedule
    plt.subplot(3, 4, 5)
    lr_history = metrics.get('lr_history', [])
    if lr_history:
        plt.plot(lr_history, color='green', alpha=0.8)
        plt.yscale('log')
        plt.xlabel('Training Step')
        plt.ylabel('Learning Rate (log scale)')
        plt.title('Learning Rate Schedule')
        plt.grid(True, alpha=0.3)

    # 6. Loss scaling over time
    plt.subplot(3, 4, 6)
    loss_scale = metrics.get('loss_scale_history', [])
    if loss_scale:
        plt.plot(loss_scale, color='purple', alpha=0.8)
        plt.xlabel('Training Step')
        plt.ylabel('Loss Scale')
        plt.title('Progressive Loss Scaling')
        plt.grid(True, alpha=0.3)

    # 7. Reward distribution
    plt.subplot(3, 4, 7)
    rewards = metrics.get('reward_history', [])
    if rewards:
        plt.hist(rewards, bins=50, alpha=0.7, color='skyblue', edgecolor='black')
        plt.xlabel('Reward Value')
        plt.ylabel('Frequency')
        plt.title('Reward Distribution')
        plt.grid(True, alpha=0.3)

    # 8. Loss distribution
    plt.subplot(3, 4, 8)
    if loss_history:
        plt.hist(loss_history, bins=50, alpha=0.7, color='lightcoral', edgecolor='black')
        plt.xlabel('Loss Value')
        plt.ylabel('Frequency')
        plt.title('Loss Distribution')
        plt.grid(True, alpha=0.3)

    # 9. Training stability (rolling statistics)
    plt.subplot(3, 4, 9)
    if loss_history and len(loss_history) > 20:
        window_size = min(50, len(loss_history) // 10)
        rolling_mean = np.convolve(loss_history, np.ones(window_size)/window_size, mode='valid')
        rolling_std = []
        for i in range(len(rolling_mean)):
            start_idx = max(0, i - window_size // 2)
            end_idx = min(len(loss_history), i + window_size // 2)
            rolling_std.append(np.std(loss_history[start_idx:end_idx]))

        x_axis = np.arange(len(rolling_mean))
        plt.plot(x_axis, rolling_mean, label='Rolling Mean', linewidth=2)
        plt.fill_between(x_axis,
                        np.array(rolling_mean) - np.array(rolling_std),
                        np.array(rolling_mean) + np.array(rolling_std),
                        alpha=0.3, label='±1 Std')
        plt.xlabel('Training Step')
        plt.ylabel('Loss')
        plt.title('Training Stability')
        plt.legend()
        plt.grid(True, alpha=0.3)

    # 10. Convergence rate analysis
    plt.subplot(3, 4, 10)
    if loss_history and len(loss_history) > 10:
        # Calculate convergence rate (derivative of loss)
        convergence_rate = np.diff(loss_history)
        plt.plot(convergence_rate, alpha=0.8, color='red')
        plt.axhline(y=0, color='black', linestyle='-', alpha=0.5)
        plt.xlabel('Training Step')
        plt.ylabel('Loss Change Rate')
        plt.title('Convergence Rate')
        plt.grid(True, alpha=0.3)

    # 11. Performance metrics summary
    plt.subplot(3, 4, 11)
    if loss_history:
        final_loss = loss_history[-1] if loss_history else 0
        min_loss = min(loss_history) if loss_history else 0
        target_loss = getattr(args, 'target_loss', 0.01)

        metrics_text = f"""
        Final Loss: {final_loss:.6f}
        Best Loss: {min_loss:.6f}
        Target Loss: {target_loss:.6f}
        Target Achieved: {'Yes' if min_loss < target_loss else 'No'}

        Steps to Target: {next((i for i, loss in enumerate(loss_history) if loss < target_loss), 'N/A')}
        Total Steps: {len(loss_history)}
        """

        plt.text(0.1, 0.5, metrics_text, fontsize=12, verticalalignment='center',
                bbox=dict(boxstyle="round,pad=0.3", facecolor="lightblue", alpha=0.8))
        plt.axis('off')
        plt.title('Performance Summary')

    # 12. Loss improvement over time
    plt.subplot(3, 4, 12)
    if loss_history and len(loss_history) > 1:
        # Calculate percentage improvement from initial loss
        initial_loss = loss_history[0]
        improvement = [(initial_loss - loss) / initial_loss * 100 for loss in loss_history]
        plt.plot(improvement, color='green', alpha=0.8)
        plt.xlabel('Training Step')
        plt.ylabel('Improvement (%)')
        plt.title('Loss Improvement from Start')
        plt.grid(True, alpha=0.3)

    plt.tight_layout()
    plt.savefig(save_path, dpi=300, bbox_inches='tight')
    plt.show()

    return fig


def analyze_convergence_statistics(metrics):
    """Analyze convergence statistics and print summary"""
    loss_history = metrics.get('loss_history', [])

    if not loss_history:
        print("No loss history available for analysis.")
        return

    print("\n" + "="*60)
    print("ENHANCED TRAINING CONVERGENCE ANALYSIS")
    print("="*60)

    # Basic statistics
    final_loss = loss_history[-1]
    min_loss = min(loss_history)
    initial_loss = loss_history[0]

    print(f"Initial Loss:     {initial_loss:.6f}")
    print(f"Final Loss:       {final_loss:.6f}")
    print(f"Best Loss:        {min_loss:.6f}")
    print(f"Total Improvement: {((initial_loss - final_loss) / initial_loss * 100):.2f}%")

    # Convergence analysis
    target_achieved_steps = [i for i, loss in enumerate(loss_history) if loss < 0.01]
    if target_achieved_steps:
        print(f"Target 0.01 achieved at step: {target_achieved_steps[0]}")
        print(f"Maintained below 0.01 for: {len(target_achieved_steps)} steps")
    else:
        print("Target 0.01 not achieved")

    # Check for different thresholds
    thresholds = [0.005, 0.001, 0.0005]
    for threshold in thresholds:
        achieved_steps = [i for i, loss in enumerate(loss_history) if loss < threshold]
        if achieved_steps:
            print(f"Target {threshold} achieved at step: {achieved_steps[0]}")

    # Stability analysis
    if len(loss_history) > 100:
        last_100_std = np.std(loss_history[-100:])
        last_100_mean = np.mean(loss_history[-100:])
        print(f"Last 100 steps - Mean: {last_100_mean:.6f}, Std: {last_100_std:.6f}")
        print(f"Coefficient of Variation: {(last_100_std / last_100_mean * 100):.2f}%")


def main():
    parser = argparse.ArgumentParser(description='Plot Enhanced Training Results')
    parser.add_argument('--checkpoint', type=str, required=True,
                       help='Path to checkpoint file')
    parser.add_argument('--output', type=str, default='enhanced_training_analysis.png',
                       help='Output plot filename')

    args = parser.parse_args()

    if not os.path.exists(args.checkpoint):
        print(f"Checkpoint file not found: {args.checkpoint}")
        return

    # Load metrics
    print(f"Loading training metrics from: {args.checkpoint}")
    metrics, training_args = load_training_metrics(args.checkpoint)

    # Create comprehensive analysis
    plot_comprehensive_training_analysis(metrics, training_args, args.output)

    # Print convergence statistics
    analyze_convergence_statistics(metrics)

    print(f"\nAnalysis plot saved to: {args.output}")


if __name__ == '__main__':
    main()