#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
test_models_with_simulation.py

Integrated testing that combines test_models_csv_format.py functionality 
with MiniGrid simulation visualization. Tests models and saves CSV results
while showing real-time robot movement and task completion.
"""

import os
import argparse
import time
import numpy as np
import pandas as pd
import torch
import matplotlib.pyplot as plt
from typing import Dict, List, Tuple

from utils import SchedulingEnv, build_hetgraph, hetgraph_node_helper
from hetnet import ScheduleNet4Layer, DecentralizedScheduleNet, MultiRobotDecentralizedSystem
from multi_objective_utils import calculate_workload_balance
from minigrid_environment import GridWorld, GridWorldVisualizer, create_grid_from_scheduling_problem


class IntegratedModelTester:
    """
    Integrated tester that combines CSV testing with MiniGrid simulation.
    """
    
    def __init__(self, device: torch.device, grid_size: int = 8, show_simulation: bool = True):
        self.device = device
        self.grid_size = grid_size
        self.show_simulation = show_simulation
        
    def load_model(self, checkpoint_path: str):
        """Load model (centralized or decentralized) from checkpoint."""
        checkpoint = torch.load(checkpoint_path, map_location=self.device)

        # Network architecture
        in_dim = {'task': 6, 'loc': 1, 'robot': 1, 'state': 4, 'value': 1}
        hid_dim = {'task': 64, 'loc': 64, 'robot': 64, 'state': 64, 'value': 64}
        out_dim = {'task': 32, 'loc': 32, 'robot': 32, 'state': 32, 'value': 1}
        cetypes = [
            ('task', 'temporal', 'task'),
            ('task', 'located_in', 'loc'), ('loc', 'near', 'loc'),
            ('task', 'assigned_to', 'robot'), ('robot', 'com', 'robot'),
            ('task', 'tin', 'state'), ('loc', 'lin', 'state'),
            ('robot', 'rin', 'state'), ('state', 'sin', 'state'),
            ('task', 'tto', 'value'), ('robot', 'rto', 'value'),
            ('state', 'sto', 'value'), ('value', 'vto', 'value'),
            ('task', 'take_time', 'robot'), ('robot', 'use_time', 'task')
        ]

        # Extract training parameters
        alpha = checkpoint.get('alpha', 0.5)
        beta = checkpoint.get('beta', 0.5)
        step = checkpoint.get('step', 0)

        # Check if this is a decentralized model
        if 'robot_networks' in checkpoint:
            # Decentralized model
            num_robots = checkpoint['num_robots']
            decentralized_system = MultiRobotDecentralizedSystem(
                in_dim, hid_dim, out_dim, cetypes, num_robots, 8
            ).to(self.device)

            # Load robot network states
            robot_networks = checkpoint['robot_networks']
            for robot_id in range(num_robots):
                robot_key = f'robot_{robot_id}'
                if robot_key in robot_networks:
                    robot_net = decentralized_system.get_robot_network(robot_id)
                    robot_net.load_state_dict(robot_networks[robot_key])
                    robot_net.eval()

            return {
                'type': 'decentralized',
                'model': decentralized_system,
                'alpha': alpha,
                'beta': beta,
                'step': step,
                'num_robots': num_robots,
                'name': f'decentralized_a{alpha:.1f}_b{beta:.1f}'
            }
        else:
            # Centralized model
            policy_net = ScheduleNet4Layer(in_dim, hid_dim, out_dim, cetypes, 8).to(self.device)
            policy_net.load_state_dict(checkpoint['policy_net_state_dict'])
            policy_net.eval()

            return {
                'type': 'centralized',
                'model': policy_net,
                'alpha': alpha,
                'beta': beta,
                'step': step,
                'num_robots': 2,  # Default for centralized
                'name': f'centralized_a{alpha:.1f}_b{beta:.1f}'
            }
    
    def solve_with_simulation(self, prefix: str, num_tasks: int, model_info: Dict,
                            communication_rounds: int = 2, save_frames: bool = False) -> Tuple[float, float, Dict, float, bool]:
        """
        Solve instance with both scheduling and MiniGrid simulation.
        
        Returns:
            makespan, workload_balance, assignments, runtime, success
        """
        t0 = time.time()
        
        print(f"\n🎮 Testing {model_info['name']} on instance: {os.path.basename(prefix)}")
        
        try:
            # Create MiniGrid environment
            if self.show_simulation:
                grid_world = create_grid_from_scheduling_problem(prefix, self.grid_size)
                plt.ion()
                visualizer = GridWorldVisualizer(grid_world)
                visualizer.render()
                plt.show()
                time.sleep(1)
            
            # Create scheduling environment
            env = SchedulingEnv(prefix)
            env.set_multi_objective_params(alpha=model_info['alpha'], beta=model_info['beta'])
            ok, mm = env.check_consistency_makespan(updateDG=False)
            if not ok:
                print("❌ Problem instance is infeasible")
                return float("nan"), float("nan"), {}, time.time() - t0, False
            env.min_makespan = mm

            print(f"📊 Problem: {num_tasks} tasks, {model_info['num_robots']} robots, Min makespan: {mm}")

            # Initialize task tracking for simulation display
            if self.show_simulation:
                grid_world.task_list = list(range(1, num_tasks + 1))  # Tasks 1 to num_tasks
                grid_world.completed_tasks_list = []
                grid_world.robot_assignments = {r: [] for r in range(model_info['num_robots'])}
                grid_world.current_makespan = 0.0
                grid_world.current_workload_balance = 0.0
                grid_world.total_tasks = num_tasks
                grid_world.min_makespan = mm
                grid_world.model_info = model_info

                # Update visualization with initial info
                self._update_simulation_display(grid_world, visualizer, step_count=0)

        except Exception as e:
            print(f"❌ Error loading environment: {e}")
            return float("nan"), float("nan"), {}, time.time() - t0, False
        
        assignments = {r: [] for r in range(model_info['num_robots'])}
        feasible_flag = True
        
        # Calculate map_width dynamically
        max_coord = max(np.max(env.loc[:, 0]), np.max(env.loc[:, 1])) if len(env.loc) > 0 else 6
        map_width = max(6, max_coord + 2)
        loc_dist_threshold = max(1, map_width // 4)
        
        step_count = 0
        simulation_step = 0
        
        print(f"🚀 Starting {model_info['type']} decision making...")
        
        while True:
            unsch_tasks = env.get_unscheduled_tasks()
            if len(unsch_tasks) == 0:
                print("✅ All tasks completed!")
                break
            
            step_count += 1
            if step_count > num_tasks * 3:  # Safety check
                print("⚠️ Maximum steps reached")
                feasible_flag = False
                break
            
            print(f"\n📋 Step {step_count}: {len(unsch_tasks)} tasks remaining")
            
            # Choose solving method based on model type
            if model_info['type'] == 'decentralized':
                best_robot, best_task = self._solve_decentralized_step(
                    env, unsch_tasks, model_info, assignments, map_width, loc_dist_threshold, communication_rounds
                )
            else:  # centralized
                best_robot, best_task = self._solve_centralized_step(
                    env, unsch_tasks, model_info, map_width, loc_dist_threshold
                )
            
            if best_robot is None or best_task is None:
                print("❌ No valid decision found")
                feasible_flag = False
                break
            
            # Show decision
            current_workload = [len(assignments[r]) for r in range(model_info['num_robots'])]
            print(f"  📊 Current workload: {current_workload}")
            print(f"  🎯 Selected: Robot {best_robot} → Task {best_task}")
            
            # Execute action in scheduling environment
            success, _, done_flag = env.insert_robot(best_task, best_robot)
            if not success:
                print("❌ Failed to execute action in scheduling environment")
                feasible_flag = False
                break
            
            assignments[best_robot].append(best_task)

            # Update tracking information
            if self.show_simulation:
                grid_world.robot_assignments[best_robot].append(best_task)
                grid_world.completed_tasks_list.append(best_task)

                # Calculate current metrics
                current_workload = [len(assignments[r]) for r in range(model_info['num_robots'])]
                grid_world.current_workload_balance = calculate_workload_balance(assignments, model_info['num_robots'])

                # Simulate robot movement in grid world
                simulation_step += self._simulate_robot_movement(
                    grid_world, best_robot, best_task, visualizer, save_frames, simulation_step
                )

                # Update display with current progress
                self._update_simulation_display(grid_world, visualizer, step_count)
            
            if done_flag:
                print("🏁 All tasks scheduled!")
                break
        
        runtime = time.time() - t0
        
        if self.show_simulation:
            # Calculate final metrics for display
            final_workload = [len(assignments[r]) for r in range(model_info['num_robots'])]
            completed_tasks = len(grid_world.completed_tasks) if hasattr(grid_world, 'completed_tasks') else num_tasks

            # Update final metrics
            if feasible_flag:
                try:
                    ok_final, final_makespan = env.check_consistency_makespan(updateDG=False)
                    if ok_final:
                        grid_world.current_makespan = final_makespan
                        grid_world.current_workload_balance = calculate_workload_balance(assignments, model_info['num_robots'])
                except:
                    pass

            # Final visualization with complete information
            self._update_simulation_display(grid_world, visualizer, step_count, final=True)
            print(f"🏁 COMPLETED - Tasks: {completed_tasks}/{num_tasks}, Workload: {final_workload}")
            time.sleep(3)
            plt.ioff()
        
        if not feasible_flag:
            return float("nan"), float("nan"), assignments, runtime, False
        
        # Calculate final metrics
        try:
            ok_final, final_makespan = env.check_consistency_makespan(updateDG=False)
            if not ok_final:
                return float("nan"), float("nan"), assignments, runtime, False
            
            workload_balance = calculate_workload_balance(assignments, model_info['num_robots'])
            
            print(f"📊 Results: Makespan={final_makespan:.2f}, Balance={workload_balance:.3f}, Time={runtime:.2f}s")
            
            return final_makespan, workload_balance, assignments, runtime, True
            
        except Exception as e:
            print(f"❌ Error calculating final metrics: {e}")
            return float("nan"), float("nan"), assignments, runtime, False

    def _solve_decentralized_step(self, env, unsch_tasks, model_info, assignments, map_width, loc_dist_threshold, communication_rounds):
        """Solve one step using decentralized model."""
        robot_decisions = {}

        # Each robot makes a decision
        for robot_id in range(model_info['num_robots']):
            try:
                # Build graph from this robot's perspective
                g = build_hetgraph(
                    env.halfDG, len(env.dur), model_info['num_robots'],
                    env.dur.astype(np.float32), map_width,
                    np.array(env.loc, dtype=np.int64), loc_dist_threshold,
                    env.partials, np.array(unsch_tasks, dtype=np.int64),
                    robot_id, np.array(unsch_tasks, dtype=np.int64)
                ).to(self.device)

                # Build features
                feat_dict = hetgraph_node_helper(
                    env.halfDG.number_of_nodes(), env.partialw, env.partials,
                    env.loc, env.dur, map_width, model_info['num_robots'], len(unsch_tasks)
                )

                feat_tensors = {k: torch.tensor(v, device=self.device, dtype=torch.float32)
                               for k, v in feat_dict.items()}

                # Forward pass with communication
                with torch.no_grad():
                    outputs = model_info['model'].forward_with_communication(
                        robot_id, g, feat_tensors, communication_rounds=communication_rounds
                    )
                    q_values = outputs['value'].cpu().numpy().reshape(-1)
                    confidence = outputs['confidence'].cpu().numpy().reshape(-1)

                # Find best task for this robot
                best_idx = np.argmax(q_values)
                best_task = int(unsch_tasks[best_idx])
                best_q = float(q_values[best_idx])
                best_conf = float(confidence[best_idx])

                robot_decisions[robot_id] = {
                    'task': best_task,
                    'q_value': best_q,
                    'confidence': best_conf
                }

                print(f"  🤖 Robot {robot_id}: Task {best_task} (conf: {best_conf:.3f}, q: {best_q:.3f})")

            except Exception as e:
                print(f"  ❌ Robot {robot_id} decision error: {e}")
                robot_decisions[robot_id] = {
                    'task': None,
                    'q_value': -float('inf'),
                    'confidence': 0.0
                }

        # Conflict resolution - choose robot with highest confidence
        best_robot = None
        best_task = None
        best_confidence = -1.0

        for robot_id, decision in robot_decisions.items():
            if (decision['task'] is not None and
                decision['confidence'] > best_confidence):
                best_confidence = decision['confidence']
                best_robot = robot_id
                best_task = decision['task']

        # Fallback: if no robot has confidence, use the one with highest Q-value
        if best_robot is None:
            best_q = -float('inf')
            for robot_id, decision in robot_decisions.items():
                if (decision['task'] is not None and
                    decision['q_value'] > best_q):
                    best_q = decision['q_value']
                    best_robot = robot_id
                    best_task = decision['task']

        # WORKLOAD BALANCING: If multiple robots have similar confidence, prefer the one with fewer tasks
        if best_robot is not None:
            current_workload = [len(assignments[r]) for r in range(model_info['num_robots'])]

            # Check if there are other robots with similar confidence but fewer tasks
            similar_confidence_robots = []
            for robot_id, decision in robot_decisions.items():
                if (decision['task'] is not None and
                    abs(decision['confidence'] - best_confidence) < 0.1):  # Similar confidence
                    similar_confidence_robots.append((robot_id, decision['task'], current_workload[robot_id]))

            if len(similar_confidence_robots) > 1:
                # Choose robot with minimum workload among similar confidence robots
                best_robot, best_task, _ = min(similar_confidence_robots, key=lambda x: x[2])

        return best_robot, best_task

    def _solve_centralized_step(self, env, unsch_tasks, model_info, map_width, loc_dist_threshold):
        """Solve one step using centralized model."""
        best_r, best_t, best_q = None, None, -float("inf")

        # Evaluate each robot choice
        for r in range(model_info['num_robots']):
            try:
                # Build heterogeneous graph
                g = build_hetgraph(
                    env.halfDG, len(env.dur), model_info['num_robots'],
                    env.dur.astype(np.float32), map_width,
                    np.array(env.loc, dtype=np.int64), loc_dist_threshold,
                    env.partials, np.array(unsch_tasks, dtype=np.int64),
                    r, np.array(unsch_tasks, dtype=np.int64)
                ).to(self.device)

                # Build features
                feat_dict = hetgraph_node_helper(
                    env.halfDG.number_of_nodes(), env.partialw, env.partials,
                    env.loc, env.dur, map_width, model_info['num_robots'], len(unsch_tasks)
                )

                feat_tensors = {k: torch.tensor(v, device=self.device, dtype=torch.float32)
                               for k, v in feat_dict.items()}

                # Forward pass
                with torch.no_grad():
                    outputs = model_info['model'](g, feat_tensors)
                    q_values = outputs['value'].cpu().numpy().reshape(-1)

                # Find best task for this robot
                idx = np.argmax(q_values)
                if q_values[idx] > best_q:
                    best_q = float(q_values[idx])
                    best_r = r
                    best_t = int(unsch_tasks[idx])

            except Exception as e:
                continue

        if best_r is not None:
            print(f"  🤖 Best: Robot {best_r} → Task {best_t} (q: {best_q:.3f})")

        return best_r, best_t

    def _simulate_robot_movement(self, grid_world, robot_id, task_id, visualizer, save_frames, simulation_step):
        """Simulate robot movement to task location in grid world."""
        steps_taken = 0

        try:
            # Find corresponding goal in grid world
            task_idx = task_id - 1  # Convert to 0-based
            if task_idx < len(grid_world.goal_positions):
                goal_pos = grid_world.goal_positions[task_idx]
                robot_pos = grid_world.robot_positions[robot_id]

                print(f"  🎮 Simulating Robot {robot_id} moving to Task {task_id} at {goal_pos}")

                # Check if robot is already at goal
                if robot_pos == goal_pos:
                    print(f"  🎯 Robot {robot_id} already at Task {task_id} location!")
                    steps_taken = 0
                else:
                    # Simple path planning and execution
                    steps_taken = self._plan_and_execute_path(grid_world, robot_id, goal_pos, visualizer, save_frames, simulation_step)

                # Mark task as completed
                if not hasattr(grid_world, 'completed_tasks'):
                    grid_world.completed_tasks = set()

                if task_idx not in grid_world.completed_tasks:
                    grid_world.completed_tasks.add(task_idx)
                    grid_world.visited_goals.add(task_idx)
                    print(f"  ✅ Task {task_id} completed by Robot {robot_id}")

                    # Update visualization to show completed task with enhanced display
                    self._update_simulation_display(grid_world, visualizer, step_count=len(grid_world.completed_tasks_list))
                    time.sleep(0.8)

        except Exception as e:
            print(f"  ⚠️ Simulation error: {e}")

        return steps_taken

    def _plan_and_execute_path(self, grid_world, robot_id, goal_pos, visualizer, save_frames, simulation_step):
        """Simple path planning and execution."""
        robot_pos = grid_world.robot_positions[robot_id]
        steps_taken = 0

        while robot_pos != goal_pos and steps_taken < 50:  # Safety limit
            rx, ry = robot_pos
            gx, gy = goal_pos

            # Simple greedy movement towards goal
            action = 0  # Stay by default

            if gx > rx:  # Move down
                action = 3
            elif gx < rx:  # Move up
                action = 1
            elif gy > ry:  # Move right
                action = 2
            elif gy < ry:  # Move left
                action = 4

            # Execute movement
            success = grid_world.move_robot(robot_id, action)
            if success:
                robot_pos = grid_world.robot_positions[robot_id]
                steps_taken += 1

                # Update visualization
                distance_to_goal = abs(gx - robot_pos[0]) + abs(gy - robot_pos[1])
                visualizer.render()
                print(f"    🎮 Robot {robot_id} → Distance: {distance_to_goal}, Step: {simulation_step + steps_taken}")
                if save_frames:
                    visualizer.save_frame(f"frame_{simulation_step + steps_taken:04d}.png")
                time.sleep(0.2)  # Smooth visualization
            else:
                print(f"    ⚠️ Robot {robot_id} movement failed")
                break

        return steps_taken

    def _update_simulation_display(self, grid_world, visualizer, step_count, final=False):
        """Update the simulation display with task lists, assignments, and metrics."""
        try:
            # Clear previous text and render grid
            visualizer.render()

            # Get current figure and axis
            fig = plt.gcf()
            ax = plt.gca()

            # Prepare display information
            total_tasks = grid_world.total_tasks
            completed_count = len(grid_world.completed_tasks_list)
            remaining_tasks = [t for t in grid_world.task_list if t not in grid_world.completed_tasks_list]

            # Create comprehensive status text
            status_lines = []

            # Header
            model_name = grid_world.model_info['name']
            status_lines.append(f"🎮 {model_name} - Step {step_count}")
            status_lines.append("=" * 40)

            # Task Overview
            status_lines.append(f"📋 TASK STATUS:")
            status_lines.append(f"  Total Tasks: {total_tasks}")
            status_lines.append(f"  Completed: {completed_count}/{total_tasks}")
            status_lines.append(f"  Remaining: {remaining_tasks}")
            status_lines.append("")

            # Robot Assignments
            status_lines.append(f"🤖 ROBOT ASSIGNMENTS:")
            for robot_id in range(grid_world.model_info['num_robots']):
                robot_tasks = grid_world.robot_assignments.get(robot_id, [])
                task_count = len(robot_tasks)
                status_lines.append(f"  Robot {robot_id}: {robot_tasks} ({task_count} tasks)")
            status_lines.append("")

            # Performance Metrics
            status_lines.append(f"📊 PERFORMANCE METRICS:")

            # Workload Balance
            workload_counts = [len(grid_world.robot_assignments.get(r, [])) for r in range(grid_world.model_info['num_robots'])]
            balance_status = "🟢 Balanced" if grid_world.current_workload_balance < 1.0 else "🔴 Imbalanced"
            status_lines.append(f"  Workload Balance: {grid_world.current_workload_balance:.3f} {balance_status}")
            status_lines.append(f"  Task Distribution: {workload_counts}")

            # Makespan
            if grid_world.current_makespan > 0:
                makespan_status = "🟢 Good" if grid_world.current_makespan <= grid_world.min_makespan * 1.2 else "🔴 High"
                status_lines.append(f"  Current Makespan: {grid_world.current_makespan:.1f} {makespan_status}")
                status_lines.append(f"  Min Possible: {grid_world.min_makespan:.1f}")
            else:
                status_lines.append(f"  Min Makespan: {grid_world.min_makespan:.1f}")

            # Model Parameters
            status_lines.append("")
            status_lines.append(f"⚙️ MODEL PARAMETERS:")
            status_lines.append(f"  α (makespan): {grid_world.model_info['alpha']:.1f}")
            status_lines.append(f"  β (balance): {grid_world.model_info['beta']:.1f}")

            if final:
                status_lines.append("")
                status_lines.append("🏁 SIMULATION COMPLETE!")

                # Final assessment
                if grid_world.current_workload_balance < 0.5:
                    status_lines.append("✅ Excellent workload balance!")
                elif grid_world.current_workload_balance < 1.0:
                    status_lines.append("✅ Good workload balance")
                elif grid_world.current_workload_balance < 2.0:
                    status_lines.append("⚠️ Moderate workload imbalance")
                else:
                    status_lines.append("❌ Poor workload balance")

            # Display the status text
            status_text = "\n".join(status_lines)

            # Position text to the right of the grid
            grid_size = grid_world.grid_size
            text_x = grid_size + 0.5
            text_y = grid_size - 1

            # Add text with background
            ax.text(text_x, text_y, status_text,
                   fontsize=8, fontfamily='monospace',
                   verticalalignment='top', horizontalalignment='left',
                   bbox=dict(boxstyle="round,pad=0.5", facecolor="lightgray", alpha=0.8))

            # Adjust figure size to accommodate text
            fig.set_size_inches(16, 10)

            # Update display
            plt.draw()
            plt.pause(0.1)

        except Exception as e:
            print(f"  ⚠️ Display update error: {e}")

    def test_model_and_save_csv(self, model_info: Dict, test_data_path: str, num_tasks: int,
                               max_instances: int, output_dir: str, communication_rounds: int = 2,
                               save_frames: bool = False) -> Tuple[str, str]:
        """Test a model with simulation and save results in CSV format."""
        model_name = model_info['name']
        print(f"\n🎮 === Testing {model_name} with Simulation ===")

        # Prepare results storage
        makespan_results = []
        balance_results = []

        successful_instances = 0
        total_instances = 0

        for inst_id in range(1, max_instances + 1):
            prefix = os.path.join(test_data_path, f"{inst_id:05d}")

            # Check if instance exists
            if not os.path.isfile(f"{prefix}_dur.txt"):
                continue

            total_instances += 1
            print(f"\n🎯 [Instance {inst_id:05d}] Testing {model_name}...")

            # Test the model with simulation
            mk, balance, assigns, rt, ok = self.solve_with_simulation(
                prefix, num_tasks, model_info, communication_rounds, save_frames
            )

            # Format results to match csv_outputs structure
            instance_id_str = f"{inst_id:05d}"

            # Makespan result
            makespan_result = {
                'instance_id': instance_id_str,
                'makespan': mk if ok else float('nan'),
                'feasible': 1 if ok else 0,
                'runtime': rt
            }
            makespan_results.append(makespan_result)

            # Workload balance result
            balance_result = {
                'instance_id': instance_id_str,
                'workload_balance': balance if ok else float('nan'),
                'feasible': 1 if ok else 0,
                'runtime': rt
            }
            balance_results.append(balance_result)

            if ok:
                successful_instances += 1
                print(f"✅ SUCCESS: makespan={mk:.1f}, balance={balance:.3f}, time={rt:.3f}s")
                print(f"   📊 Task assignments: {assigns}")
            else:
                print(f"❌ FAILED: time={rt:.3f}s")

            # Pause between instances for better visualization
            if self.show_simulation and total_instances < max_instances:
                print("⏸️ Pausing 2 seconds before next instance...")
                time.sleep(2)

        # Save CSV files
        if makespan_results:
            # Save makespan results
            makespan_df = pd.DataFrame(makespan_results)
            makespan_filename = f"makespans_{model_name.lower().replace(' ', '_')}.csv"
            makespan_path = os.path.join(output_dir, makespan_filename)
            makespan_df.to_csv(makespan_path, index=False)

            # Save workload balance results
            balance_df = pd.DataFrame(balance_results)
            balance_filename = f"workload_balance_{model_name.lower().replace(' ', '_')}.csv"
            balance_path = os.path.join(output_dir, balance_filename)
            balance_df.to_csv(balance_path, index=False)

            # Print summary
            print(f"\n📊 {model_name} Summary:")
            print(f"  Instances tested: {total_instances}")
            print(f"  Feasible solutions: {successful_instances}/{total_instances} ({successful_instances/total_instances*100:.1f}%)")

            if successful_instances > 0:
                feasible_makespan = makespan_df[makespan_df['feasible'] == 1]
                feasible_balance = balance_df[balance_df['feasible'] == 1]

                avg_makespan = feasible_makespan['makespan'].mean()
                avg_balance = feasible_balance['workload_balance'].mean()
                avg_runtime = feasible_makespan['runtime'].mean()

                print(f"  Average makespan: {avg_makespan:.2f}")
                print(f"  Average workload balance: {avg_balance:.3f}")
                print(f"  Average runtime: {avg_runtime:.3f}s")

            print(f"  📊 Makespan results saved to: {makespan_path}")
            print(f"  📊 Workload balance results saved to: {balance_path}")

            return makespan_path, balance_path
        else:
            print(f"No results for {model_name}")
            return None, None


def solve_with_baseline(prefix: str, num_tasks: int, num_robots: int,
                       strategy: str = "random") -> Tuple[float, float, Dict, float, bool]:
    """Solve using baseline strategy (for comparison)."""
    t0 = time.time()

    try:
        env = SchedulingEnv(prefix)
        ok, mm = env.check_consistency_makespan(updateDG=False)
        if not ok:
            return float("nan"), float("nan"), {}, time.time() - t0, False
        env.min_makespan = mm
    except Exception as e:
        return float("nan"), float("nan"), {}, time.time() - t0, False

    assignments = {r: [] for r in range(num_robots)}
    feasible_flag = True

    step_count = 0
    while True:
        unsch_tasks = env.get_unscheduled_tasks()
        if len(unsch_tasks) == 0:
            break

        step_count += 1
        if step_count > num_tasks * 2:  # Safety check
            feasible_flag = False
            break

        # Choose task and robot based on strategy
        try:
            if strategy == "random":
                task = np.random.choice(unsch_tasks)
                robot = np.random.randint(0, num_robots)
            elif strategy == "round_robin":
                task = unsch_tasks[0]  # First available task
                robot = step_count % num_robots  # Round-robin robot assignment
            elif strategy == "greedy_balance":
                # Choose robot with fewest assigned tasks
                task = unsch_tasks[0]  # First available task
                task_counts = [len(assignments[r]) for r in range(num_robots)]
                robot = np.argmin(task_counts)
            else:
                # Default to random
                task = np.random.choice(unsch_tasks)
                robot = np.random.randint(0, num_robots)
        except Exception as e:
            task = unsch_tasks[0]
            robot = 0

        # Execute action
        success, _, done_flag = env.insert_robot(task, robot)
        if not success:
            feasible_flag = False
            break

        assignments[robot].append(task)

        if done_flag:
            break

    runtime = time.time() - t0

    if not feasible_flag:
        return float("nan"), float("nan"), assignments, runtime, False

    # Calculate final metrics
    try:
        ok_final, final_makespan = env.check_consistency_makespan(updateDG=False)
        if not ok_final:
            return float("nan"), float("nan"), assignments, runtime, False

        workload_balance = calculate_workload_balance(assignments, num_robots)

        return final_makespan, workload_balance, assignments, runtime, True

    except Exception as e:
        return float("nan"), float("nan"), assignments, runtime, False


def main():
    parser = argparse.ArgumentParser(description="Integrated Model Testing with MiniGrid Simulation")
    parser.add_argument("--model-path", help="Path to trained model checkpoint")
    parser.add_argument("--test-data", default="./problem_instances/constraints",
                       help="Path to test data directory")
    parser.add_argument("--num-tasks", default=5, type=int, help="Number of tasks")
    parser.add_argument("--max-instances", type=int, default=10, help="Maximum instances to test")
    parser.add_argument("--output-dir", default="./csv_outputs_simulation", help="Output directory")
    parser.add_argument("--grid-size", type=int, default=8, help="Grid size for simulation")
    parser.add_argument("--communication-rounds", type=int, default=2, help="Communication rounds for decentralized")
    parser.add_argument("--device", default="cpu", help="Device (cpu or cuda)")
    parser.add_argument("--no-simulation", action="store_true", help="Disable visual simulation")
    parser.add_argument("--save-frames", action="store_true", help="Save simulation frames")
    parser.add_argument("--test-baselines", action="store_true", help="Also test baseline methods")
    parser.add_argument("--seed", type=int, default=42, help="Random seed")

    args = parser.parse_args()

    # Set random seed
    np.random.seed(args.seed)
    torch.manual_seed(args.seed)

    device = torch.device(args.device)
    os.makedirs(args.output_dir, exist_ok=True)

    print("🎮 INTEGRATED MODEL TESTING WITH MINIGRID SIMULATION")
    print("=" * 70)
    print(f"📁 Test data: {args.test_data}")
    print(f"📊 Problem size: {args.num_tasks} tasks")
    print(f"🔢 Max instances: {args.max_instances}")
    print(f"🎮 Grid size: {args.grid_size}×{args.grid_size}")
    print(f"📂 Output directory: {args.output_dir}")
    print(f"🎬 Show simulation: {not args.no_simulation}")
    print(f"📸 Save frames: {args.save_frames}")

    # Create integrated tester
    tester = IntegratedModelTester(device, args.grid_size, show_simulation=not args.no_simulation)

    models_to_test = []

    # Add model if provided
    if args.model_path and os.path.isfile(args.model_path):
        try:
            model_info = tester.load_model(args.model_path)
            models_to_test.append(model_info)
            print(f"✅ Loaded {model_info['type']} model: {model_info['name']}")
            print(f"   α={model_info['alpha']:.3f}, β={model_info['beta']:.3f}, step={model_info['step']}")
        except Exception as e:
            print(f"❌ Failed to load model {args.model_path}: {e}")

    # Add baseline models if requested
    if args.test_baselines:
        baseline_strategies = ["random", "round_robin", "greedy_balance"]
        for strategy in baseline_strategies:
            models_to_test.append({
                'name': strategy,
                'type': 'baseline',
                'strategy': strategy,
                'num_robots': 2,  # Default for baselines
                'alpha': 0.5,
                'beta': 0.5
            })
        print(f"✅ Added {len(baseline_strategies)} baseline models")

    if not models_to_test:
        print("❌ No models to test. Please provide --model-path or use --test-baselines")
        return

    # Test each model
    csv_files = []
    for model_info in models_to_test:
        if model_info['type'] == 'baseline':
            # Test baseline without simulation
            makespan_csv, balance_csv = test_baseline_and_save_csv(
                model_info, args.test_data, args.num_tasks, args.max_instances, args.output_dir
            )
        else:
            # Test with simulation
            makespan_csv, balance_csv = tester.test_model_and_save_csv(
                model_info, args.test_data, args.num_tasks, args.max_instances,
                args.output_dir, args.communication_rounds, args.save_frames
            )

        if makespan_csv:
            csv_files.extend([makespan_csv, balance_csv])

    # Final summary
    if csv_files:
        print(f"\n🎉 === INTEGRATED TESTING COMPLETED ===")
        print(f"📊 Generated {len(csv_files)} CSV files:")
        for csv_file in csv_files:
            print(f"  - {os.path.basename(csv_file)}")
        print(f"\n📁 All files saved in: {args.output_dir}")
        print(f"🎮 Testing completed with MiniGrid simulation integration!")
    else:
        print("❌ No CSV files generated.")


def test_baseline_and_save_csv(model_info: Dict, test_data_path: str, num_tasks: int,
                              max_instances: int, output_dir: str) -> Tuple[str, str]:
    """Test baseline model and save CSV (without simulation)."""
    model_name = model_info['name']
    print(f"\n📊 === Testing {model_name} (baseline) ===")

    makespan_results = []
    balance_results = []
    successful_instances = 0
    total_instances = 0

    for inst_id in range(1, max_instances + 1):
        prefix = os.path.join(test_data_path, f"{inst_id:05d}")

        if not os.path.isfile(f"{prefix}_dur.txt"):
            continue

        total_instances += 1
        print(f"[Instance {inst_id:05d}] Testing {model_name}...")

        mk, balance, assigns, rt, ok = solve_with_baseline(
            prefix, num_tasks, model_info['num_robots'], model_info['strategy']
        )

        instance_id_str = f"{inst_id:05d}"

        makespan_results.append({
            'instance_id': instance_id_str,
            'makespan': mk if ok else float('nan'),
            'feasible': 1 if ok else 0,
            'runtime': rt
        })

        balance_results.append({
            'instance_id': instance_id_str,
            'workload_balance': balance if ok else float('nan'),
            'feasible': 1 if ok else 0,
            'runtime': rt
        })

        if ok:
            successful_instances += 1
            print(f"  ✓ makespan={mk:.1f}, balance={balance:.3f}, time={rt:.3f}s")
        else:
            print(f"  ✗ infeasible, time={rt:.3f}s")

    # Save CSV files
    if makespan_results:
        makespan_df = pd.DataFrame(makespan_results)
        makespan_filename = f"makespans_{model_name.lower().replace(' ', '_')}.csv"
        makespan_path = os.path.join(output_dir, makespan_filename)
        makespan_df.to_csv(makespan_path, index=False)

        balance_df = pd.DataFrame(balance_results)
        balance_filename = f"workload_balance_{model_name.lower().replace(' ', '_')}.csv"
        balance_path = os.path.join(output_dir, balance_filename)
        balance_df.to_csv(balance_path, index=False)

        print(f"\n📊 {model_name} Summary:")
        print(f"  Instances tested: {total_instances}")
        print(f"  Feasible solutions: {successful_instances}/{total_instances} ({successful_instances/total_instances*100:.1f}%)")

        if successful_instances > 0:
            feasible_makespan = makespan_df[makespan_df['feasible'] == 1]
            feasible_balance = balance_df[balance_df['feasible'] == 1]

            print(f"  Average makespan: {feasible_makespan['makespan'].mean():.2f}")
            print(f"  Average workload balance: {feasible_balance['workload_balance'].mean():.3f}")

        print(f"  📊 Results saved to: {makespan_path}, {balance_path}")
        return makespan_path, balance_path

    return None, None


if __name__ == "__main__":
    main()
