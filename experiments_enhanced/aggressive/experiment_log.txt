Experiment: Aggressive Training
Success: False
Configuration: {'name': 'Aggressive Training', 'lr': 0.0002, 'weight_decay': 1e-07, 'num_robots': 2, 'num_steps': 5000, 'batch_size': 32, 'checkpoint_interval': 50, 'checkpoint_dir': './experiments_enhanced/aggressive', 'loss_type': 'multi_objective', 'target_loss': 0.003, 'dropout': 0.15, 'num_heads': 12, 'use_warmup': True}

STDOUT:


STDERR:
[Errno 2] No such file or directory: 'python'