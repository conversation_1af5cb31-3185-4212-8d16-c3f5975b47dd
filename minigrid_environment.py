#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
minigrid_environment.py

MiniGrid environment for multi-robot task scheduling simulation.
Creates a grid world where robots move to complete tasks at different locations.
"""

import numpy as np
import matplotlib.pyplot as plt
import matplotlib.patches as patches
from typing import Dict, List, Tuple, Optional
import random


class GridWorld:
    """
    Grid world environment for multi-robot task scheduling.
    """
    
    def __init__(self, grid_size: int = 10, num_robots: int = 2, num_goals: int = 5):
        self.grid_size = grid_size
        self.num_robots = num_robots
        self.num_goals = num_goals
        
        # Initialize grid
        self.grid = np.zeros((grid_size, grid_size), dtype=int)
        
        # Robot positions (x, y)
        self.robot_positions = []
        self.robot_paths = [[] for _ in range(num_robots)]
        
        # Goal positions and states
        self.goal_positions = []
        self.visited_goals = set()
        self.task_assignments = {}  # robot_id -> goal_id
        
        # Initialize positions
        self._initialize_positions()
        
        print(f"GridWorld initialized: {grid_size}x{grid_size}, {num_robots} robots, {num_goals} goals")
    
    def _initialize_positions(self):
        """Initialize robot and goal positions randomly."""
        # Place robots randomly
        for i in range(self.num_robots):
            while True:
                x, y = random.randint(0, self.grid_size-1), random.randint(0, self.grid_size-1)
                if self.grid[x, y] == 0:  # Empty cell
                    self.robot_positions.append((x, y))
                    self.grid[x, y] = i + 1  # Robot ID (1-based)
                    break
        
        # Place goals randomly
        for i in range(self.num_goals):
            while True:
                x, y = random.randint(0, self.grid_size-1), random.randint(0, self.grid_size-1)
                if self.grid[x, y] == 0:  # Empty cell
                    self.goal_positions.append((x, y))
                    self.grid[x, y] = -(i + 1)  # Goal ID (negative for distinction)
                    break
    
    def move_robot(self, robot_id: int, action: int) -> bool:
        """
        Move robot in the grid.
        Actions: 0=stay, 1=up, 2=right, 3=down, 4=left
        Returns True if move was successful.
        """
        if robot_id >= self.num_robots:
            return False
        
        current_pos = self.robot_positions[robot_id]
        new_pos = current_pos
        
        # Calculate new position based on action
        if action == 1:  # Up
            new_pos = (current_pos[0] - 1, current_pos[1])
        elif action == 2:  # Right
            new_pos = (current_pos[0], current_pos[1] + 1)
        elif action == 3:  # Down
            new_pos = (current_pos[0] + 1, current_pos[1])
        elif action == 4:  # Left
            new_pos = (current_pos[0], current_pos[1] - 1)
        # action == 0 means stay in place
        
        # Check bounds
        if (new_pos[0] < 0 or new_pos[0] >= self.grid_size or 
            new_pos[1] < 0 or new_pos[1] >= self.grid_size):
            return False
        
        # Check if new position is occupied by another robot
        if self.grid[new_pos[0], new_pos[1]] > 0 and new_pos != current_pos:
            return False
        
        # Update grid
        if new_pos != current_pos:
            # Clear old position (but keep goal if it was there)
            old_cell_value = self.grid[current_pos[0], current_pos[1]]
            if old_cell_value == robot_id + 1:  # Only robot was there
                self.grid[current_pos[0], current_pos[1]] = 0
            
            # Set new position
            goal_at_new_pos = self.grid[new_pos[0], new_pos[1]]
            self.grid[new_pos[0], new_pos[1]] = robot_id + 1
            
            # Check if robot reached a goal
            if goal_at_new_pos < 0:  # Negative means goal
                goal_id = abs(goal_at_new_pos) - 1
                if goal_id not in self.visited_goals:
                    self.visited_goals.add(goal_id)
                    print(f"Robot {robot_id} reached goal {goal_id} at {new_pos}")
            
            # Update robot position
            self.robot_positions[robot_id] = new_pos
            self.robot_paths[robot_id].append(new_pos)
        
        return True
    
    def assign_task(self, robot_id: int, goal_id: int):
        """Assign a goal to a robot."""
        self.task_assignments[robot_id] = goal_id
    
    def get_state(self) -> Dict:
        """Get current state of the grid world."""
        return {
            'robot_positions': self.robot_positions.copy(),
            'goal_positions': self.goal_positions.copy(),
            'visited_goals': self.visited_goals.copy(),
            'task_assignments': self.task_assignments.copy(),
            'completion_rate': len(self.visited_goals) / self.num_goals,
            'grid': self.grid.copy()
        }
    
    def is_complete(self) -> bool:
        """Check if all goals have been visited."""
        return len(self.visited_goals) >= self.num_goals
    
    def reset(self):
        """Reset the grid world to initial state."""
        self.grid = np.zeros((self.grid_size, self.grid_size), dtype=int)
        self.robot_positions = []
        self.robot_paths = [[] for _ in range(self.num_robots)]
        self.goal_positions = []
        self.visited_goals = set()
        self.task_assignments = {}
        self._initialize_positions()


class GridWorldVisualizer:
    """
    Visualizer for the grid world environment.
    """
    
    def __init__(self, grid_world: GridWorld):
        self.grid_world = grid_world
        self.fig, self.ax = plt.subplots(figsize=(10, 10))
        self.robot_colors = ['red', 'blue', 'green', 'orange', 'purple']
        self.goal_colors = ['lightcoral', 'lightblue', 'lightgreen', 'moccasin', 'plum']
    
    def render(self, show_paths: bool = True, title: str = "Multi-Robot Grid World"):
        """Render the current state of the grid world."""
        self.ax.clear()
        self.ax.set_xlim(-0.5, self.grid_world.grid_size - 0.5)
        self.ax.set_ylim(-0.5, self.grid_world.grid_size - 0.5)
        self.ax.set_aspect('equal')
        self.ax.set_title(title)
        
        # Draw grid
        for i in range(self.grid_world.grid_size + 1):
            self.ax.axhline(i - 0.5, color='gray', linewidth=0.5)
            self.ax.axvline(i - 0.5, color='gray', linewidth=0.5)
        
        # Draw goals
        for goal_id, (gx, gy) in enumerate(self.grid_world.goal_positions):
            color = self.goal_colors[goal_id % len(self.goal_colors)]
            if goal_id in self.grid_world.visited_goals:
                # Visited goal - darker color
                color = self.goal_colors[goal_id % len(self.goal_colors)].replace('light', '')
            
            goal_patch = patches.Rectangle((gy - 0.4, gx - 0.4), 0.8, 0.8, 
                                         linewidth=2, edgecolor='black', 
                                         facecolor=color, alpha=0.7)
            self.ax.add_patch(goal_patch)
            self.ax.text(gy, gx, f'G{goal_id}', ha='center', va='center', fontweight='bold')
        
        # Draw robot paths
        if show_paths:
            for robot_id, path in enumerate(self.grid_world.robot_paths):
                if len(path) > 1:
                    path_x = [p[1] for p in path]
                    path_y = [p[0] for p in path]
                    color = self.robot_colors[robot_id % len(self.robot_colors)]
                    self.ax.plot(path_x, path_y, color=color, alpha=0.5, linewidth=2, linestyle='--')
        
        # Draw robots
        for robot_id, (rx, ry) in enumerate(self.grid_world.robot_positions):
            color = self.robot_colors[robot_id % len(self.robot_colors)]
            robot_circle = patches.Circle((ry, rx), 0.3, linewidth=2, 
                                        edgecolor='black', facecolor=color)
            self.ax.add_patch(robot_circle)
            self.ax.text(ry, rx, f'R{robot_id}', ha='center', va='center', 
                        fontweight='bold', color='white')
        
        # Add legend
        legend_elements = []
        for i in range(self.grid_world.num_robots):
            color = self.robot_colors[i % len(self.robot_colors)]
            legend_elements.append(patches.Patch(color=color, label=f'Robot {i}'))
        
        for i in range(self.grid_world.num_goals):
            color = self.goal_colors[i % len(self.goal_colors)]
            legend_elements.append(patches.Patch(color=color, label=f'Goal {i}'))
        
        self.ax.legend(handles=legend_elements, loc='upper left', bbox_to_anchor=(1, 1))
        
        # Add status text
        status_text = f"Goals: {len(self.grid_world.visited_goals)}/{self.grid_world.num_goals}\n"
        status_text += f"Completion: {len(self.grid_world.visited_goals)/self.grid_world.num_goals*100:.1f}%"
        self.ax.text(0.02, 0.98, status_text, transform=self.ax.transAxes, 
                    verticalalignment='top', bbox=dict(boxstyle="round,pad=0.3", 
                    facecolor="white", alpha=0.8))
        
        plt.tight_layout()
        plt.draw()
        plt.pause(0.1)
    
    def save_frame(self, filename: str):
        """Save current frame as image."""
        plt.savefig(filename, dpi=150, bbox_inches='tight')


def create_scheduling_problem_from_grid(grid_world: GridWorld) -> Optional[Dict]:
    """
    Convert grid world state to scheduling problem format.
    This creates a scheduling problem based on current robot positions and unvisited goals.
    """
    unvisited_goals = []
    for goal_id in range(grid_world.num_goals):
        if goal_id not in grid_world.visited_goals:
            unvisited_goals.append(goal_id)
    
    if len(unvisited_goals) == 0:
        return None
    
    num_tasks = len(unvisited_goals)
    num_robots = grid_world.num_robots
    
    # Create duration matrix (distance-based)
    durations = np.zeros((num_tasks, num_robots), dtype=int)
    locations = []
    
    for task_idx, goal_id in enumerate(unvisited_goals):
        goal_pos = grid_world.goal_positions[goal_id]
        locations.append(goal_pos)
        
        for robot_id in range(num_robots):
            robot_pos = grid_world.robot_positions[robot_id]
            # Manhattan distance + base task duration
            distance = abs(goal_pos[0] - robot_pos[0]) + abs(goal_pos[1] - robot_pos[1])
            durations[task_idx, robot_id] = max(1, distance + random.randint(1, 3))
    
    # Create simple deadlines (generous)
    deadlines = np.full(num_tasks, 50, dtype=int)
    
    # Create wait constraints (minimal)
    wait_constraints = np.zeros((num_tasks, num_tasks), dtype=int)
    
    return {
        'num_tasks': num_tasks,
        'num_robots': num_robots,
        'durations': durations,
        'deadlines': deadlines,
        'wait_constraints': wait_constraints,
        'locations': np.array(locations),
        'unvisited_goals': unvisited_goals
    }


def create_grid_from_scheduling_problem(problem_prefix: str, grid_size: int = 10) -> GridWorld:
    """
    Create a grid world from a scheduling problem instance.
    This converts problem instance files into a grid scenario.
    """
    try:
        # Load problem data
        durations = np.loadtxt(f"{problem_prefix}_dur.txt", dtype=int)
        locations = np.loadtxt(f"{problem_prefix}_loc.txt", dtype=int)
        
        num_tasks, num_robots = durations.shape
        
        # Create grid world
        grid_world = GridWorld(grid_size, num_robots, num_tasks)
        
        # Clear default positions
        grid_world.grid = np.zeros((grid_size, grid_size), dtype=int)
        grid_world.robot_positions = []
        grid_world.goal_positions = []
        grid_world.visited_goals = set()
        
        # Place robots at corners
        robot_start_positions = [
            (0, 0), (0, grid_size-1), (grid_size-1, 0), (grid_size-1, grid_size-1),
            (grid_size//2, 0), (grid_size//2, grid_size-1), (0, grid_size//2), (grid_size-1, grid_size//2)
        ]
        
        for robot_id in range(num_robots):
            if robot_id < len(robot_start_positions):
                pos = robot_start_positions[robot_id]
            else:
                # Random position for additional robots
                pos = (random.randint(0, grid_size-1), random.randint(0, grid_size-1))
            
            grid_world.robot_positions.append(pos)
            grid_world.grid[pos[0], pos[1]] = robot_id + 1
            grid_world.robot_paths[robot_id] = [pos]
        
        # Place goals based on location data (scaled to grid)
        if len(locations) > 0:
            # Scale locations to fit grid
            max_loc = np.max(locations)
            scale_factor = (grid_size - 1) / max(max_loc, 1)
            
            for task_id in range(num_tasks):
                if task_id < len(locations):
                    scaled_x = int(locations[task_id, 0] * scale_factor)
                    scaled_y = int(locations[task_id, 1] * scale_factor)
                else:
                    scaled_x = random.randint(0, grid_size-1)
                    scaled_y = random.randint(0, grid_size-1)
                
                # Ensure goal doesn't overlap with robots
                while grid_world.grid[scaled_x, scaled_y] > 0:
                    scaled_x = (scaled_x + 1) % grid_size
                    scaled_y = (scaled_y + 1) % grid_size
                
                grid_world.goal_positions.append((scaled_x, scaled_y))
                grid_world.grid[scaled_x, scaled_y] = -(task_id + 1)
        else:
            # Random goal placement
            for task_id in range(num_tasks):
                while True:
                    x, y = random.randint(0, grid_size-1), random.randint(0, grid_size-1)
                    if grid_world.grid[x, y] == 0:
                        grid_world.goal_positions.append((x, y))
                        grid_world.grid[x, y] = -(task_id + 1)
                        break
        
        return grid_world
        
    except Exception as e:
        print(f"Error creating grid from problem: {e}")
        # Fallback to random grid
        return GridWorld(grid_size, 2, 5)
