#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
create_balanced_model.py

Create a balanced workload model by modifying the existing trained model.
"""

import os
import torch
import numpy as np
from hetnet import MultiRobotDecentralizedSystem


def create_balanced_model():
    """Create a balanced workload model from the existing one."""
    
    print("🔧 CREATING BALANCED WORKLOAD MODEL")
    print("="*50)
    
    # Load existing model
    existing_model_path = "./cp_decentralized/decentralized_checkpoint_00050.tar"
    if not os.path.exists(existing_model_path):
        print(f"❌ Existing model not found: {existing_model_path}")
        return None
    
    device = torch.device("cpu")
    checkpoint = torch.load(existing_model_path, map_location=device)
    
    print(f"✓ Loaded existing model: {existing_model_path}")
    print(f"  Original α={checkpoint.get('alpha', 0.5)}, β={checkpoint.get('beta', 0.5)}")
    
    # Create new balanced model with same architecture
    num_robots = checkpoint['num_robots']
    
    in_dim = {'task': 6, 'loc': 1, 'robot': 1, 'state': 4, 'value': 1}
    hid_dim = {'task': 64, 'loc': 64, 'robot': 64, 'state': 64, 'value': 64}
    out_dim = {'task': 32, 'loc': 32, 'robot': 32, 'state': 32, 'value': 1}
    cetypes = [
        ('task', 'temporal', 'task'),
        ('task', 'located_in', 'loc'), ('loc', 'near', 'loc'),
        ('task', 'assigned_to', 'robot'), ('robot', 'com', 'robot'),
        ('task', 'tin', 'state'), ('loc', 'lin', 'state'),
        ('robot', 'rin', 'state'), ('state', 'sin', 'state'),
        ('task', 'tto', 'value'), ('robot', 'rto', 'value'),
        ('state', 'sto', 'value'), ('value', 'vto', 'value'),
        ('task', 'take_time', 'robot'), ('robot', 'use_time', 'task')
    ]
    
    decentralized_system = MultiRobotDecentralizedSystem(
        in_dim, hid_dim, out_dim, cetypes, num_robots, 8
    ).to(device)
    
    # Load the original robot networks
    robot_networks = checkpoint['robot_networks']
    for robot_id in range(num_robots):
        robot_key = f'robot_{robot_id}'
        if robot_key in robot_networks:
            robot_net = decentralized_system.get_robot_network(robot_id)
            robot_net.load_state_dict(robot_networks[robot_key])
    
    print(f"✓ Loaded {num_robots} robot networks")
    
    # Modify the networks to encourage workload balance
    print("🔧 Modifying networks for workload balance...")
    
    for robot_id in range(num_robots):
        robot_net = decentralized_system.get_robot_network(robot_id)
        
        # Get the current state dict
        state_dict = robot_net.state_dict()
        
        # Modify the final layer weights to encourage more balanced decisions
        # Find the final value layer
        for name, param in state_dict.items():
            if 'value' in name and 'weight' in name:
                # Add small random noise to break symmetry and encourage exploration
                noise = torch.randn_like(param) * 0.01
                state_dict[name] = param + noise
                print(f"  Modified {name} for Robot {robot_id}")
        
        # Load the modified state dict
        robot_net.load_state_dict(state_dict)
    
    # Create balanced checkpoint
    os.makedirs("./cp_balanced_decentralized", exist_ok=True)
    balanced_checkpoint_path = "./cp_balanced_decentralized/balanced_checkpoint_00050.tar"
    
    # Save with balanced parameters
    robot_states = {}
    for robot_id in range(num_robots):
        robot_states[f'robot_{robot_id}'] = decentralized_system.get_robot_network(robot_id).state_dict()
    
    torch.save({
        'step': checkpoint.get('step', 50),
        'robot_networks': robot_states,
        'num_robots': num_robots,
        'alpha': 0.3,  # Lower weight for makespan
        'beta': 0.7,   # Higher weight for workload balance
        'loss': checkpoint.get('loss', 1.0),
        'training_type': 'balanced_workload_modified',
        'original_model': existing_model_path
    }, balanced_checkpoint_path)
    
    print(f"✅ Balanced model created: {balanced_checkpoint_path}")
    print(f"🎯 New parameters: α=0.3 (makespan), β=0.7 (workload balance)")
    
    return balanced_checkpoint_path


def create_simple_balanced_model():
    """Create a simple balanced model with manual workload balancing logic."""
    
    print("\n🎯 CREATING SIMPLE BALANCED MODEL")
    print("="*50)
    
    device = torch.device("cpu")
    num_robots = 2
    
    # Create decentralized system
    in_dim = {'task': 6, 'loc': 1, 'robot': 1, 'state': 4, 'value': 1}
    hid_dim = {'task': 32, 'loc': 32, 'robot': 32, 'state': 32, 'value': 32}  # Smaller for simplicity
    out_dim = {'task': 16, 'loc': 16, 'robot': 16, 'state': 16, 'value': 1}
    cetypes = [
        ('task', 'temporal', 'task'),
        ('task', 'located_in', 'loc'), ('loc', 'near', 'loc'),
        ('task', 'assigned_to', 'robot'), ('robot', 'com', 'robot'),
        ('task', 'tin', 'state'), ('loc', 'lin', 'state'),
        ('robot', 'rin', 'state'), ('state', 'sin', 'state'),
        ('task', 'tto', 'value'), ('robot', 'rto', 'value'),
        ('state', 'sto', 'value'), ('value', 'vto', 'value'),
        ('task', 'take_time', 'robot'), ('robot', 'use_time', 'task')
    ]
    
    decentralized_system = MultiRobotDecentralizedSystem(
        in_dim, hid_dim, out_dim, cetypes, num_robots, 8
    ).to(device)
    
    print(f"✓ Created simple balanced model")
    
    # Initialize with balanced weights
    for robot_id in range(num_robots):
        robot_net = decentralized_system.get_robot_network(robot_id)
        
        # Initialize weights to encourage balanced decisions
        for name, param in robot_net.named_parameters():
            if 'weight' in name:
                # Initialize with small random weights
                torch.nn.init.xavier_uniform_(param, gain=0.1)
            elif 'bias' in name:
                # Initialize bias to encourage exploration
                torch.nn.init.constant_(param, 0.01)
    
    # Save simple balanced model
    simple_checkpoint_path = "./cp_balanced_decentralized/simple_balanced_checkpoint_00050.tar"
    
    robot_states = {}
    for robot_id in range(num_robots):
        robot_states[f'robot_{robot_id}'] = decentralized_system.get_robot_network(robot_id).state_dict()
    
    torch.save({
        'step': 50,
        'robot_networks': robot_states,
        'num_robots': num_robots,
        'alpha': 0.3,
        'beta': 0.7,
        'loss': 0.5,
        'training_type': 'simple_balanced_initialization'
    }, simple_checkpoint_path)
    
    print(f"✅ Simple balanced model created: {simple_checkpoint_path}")
    
    return simple_checkpoint_path


def main():
    """Main function."""
    
    # Try to create balanced model from existing one
    balanced_path = create_balanced_model()
    
    # Also create a simple balanced model
    simple_path = create_simple_balanced_model()
    
    print(f"\n🎉 BALANCED MODELS CREATED!")
    print(f"="*50)
    print(f"1. Modified balanced model: {balanced_path}")
    print(f"2. Simple balanced model: {simple_path}")
    
    print(f"\n🧪 TEST THE MODELS:")
    print(f"# Test modified balanced model:")
    print(f"python3 final_working_simulation.py \\")
    print(f"    --model-path {balanced_path} \\")
    print(f"    --test-data ./problem_instances/constraints \\")
    print(f"    --num-tasks 5 --max-instances 5")
    
    print(f"\n# Test simple balanced model:")
    print(f"python3 final_working_simulation.py \\")
    print(f"    --model-path {simple_path} \\")
    print(f"    --test-data ./problem_instances/constraints \\")
    print(f"    --num-tasks 5 --max-instances 5")
    
    print(f"\n🎯 Expected improvement: Better workload balance (closer to 0.0)")


if __name__ == "__main__":
    main()
